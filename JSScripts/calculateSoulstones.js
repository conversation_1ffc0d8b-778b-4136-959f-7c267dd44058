/**
 * 以分钟与流转类型计算领域
 * <AUTHOR>
 * @version 0.1.1
 * @lastUpdate 2025-02-01
 */

// 配置常量
const CONFIG = {
    DIARY_PATH_PREFIX: "日记/",
    FRONTMATTER_KEY: "灵石",
    FLOW_TYPE_KEY: "流转",
    FLOW_TYPES: {
        DEEP:   ["学习", "实践"],
        ROAM:   ["探索"],
        LOOP:   ["练习"],
        MOTION: ["运动"]
    },
    TIME_THRESHOLDS: {
        DEEP:   [240,420,600],
        ROAM:   [120,180,360],
        LOOP:   [60,120,240],
        MOTION: [60,240,420]
    },
    TIME_BONUS: {
        DEEP:   [1.0, 1.4, 0.6],
        ROAM:   [1.0, 1.3, 0.9],
        LOOP:   [1.0, 1.6, 0.7],
        MOTION: [1.0, 1.0, 1.0]
    }
};

function calculateSoulstones(minutes, flowType) {
    const timeThresholds = CONFIG.TIME_THRESHOLDS[flowType];
    const timeBonus = CONFIG.TIME_BONUS[flowType];

    let soulstones = 0;
    for (let i = 0; i < timeThresholds.length; i++) {
        soulstones += timeBonus[i] * Math.min(minutes, timeThresholds[i]);
        minutes -= timeThresholds[i];
        if (minutes <= 0) break;
    }

    return soulstones;
}

function main(totalMinutes, flowType) {
    // 查找匹配的流转类型
    const flowCategory = Object.entries(CONFIG.FLOW_TYPES)
        .find(([category, types]) => types.includes(flowType))?.[0];

    // 如果找到匹配的类型则计算灵石,否则返回0
    const soulstones = flowCategory
        ? calculateSoulstones(totalMinutes, flowCategory) / 60.0
        : 0.0;

    // 格式化并限制最大值
    return {
        number: soulstones,
        type: flowType
    };
}

module.exports = main;