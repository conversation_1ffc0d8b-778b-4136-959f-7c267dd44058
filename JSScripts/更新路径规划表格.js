function updateTableValues(content) {
    // 使用正则表达式匹配表格
    const tableRegex = /^\|(.+\|)+$\n(^\|(:?-+:?\|)+$\n)?(^\|(.+\|)+$\n?)+/gm;

    // 查找原有表格
    const match = content.match(tableRegex);
    if (!match) {
        console.log("未找到表格，将使用原有逻辑处理整个内容");
        return content;
    }

    // 提取原有表格的内容
    const oldTable = match[0];

    // 使用原有的更新逻辑生成新表格
    const {content: updatedTable, fm} = 更新逻辑(oldTable);

    // 替换原有的表格为新表格
    return {content: content.replace(oldTable, updatedTable), fm};
}

// 修改更新逻辑函数
function 更新逻辑(content) {
    // 使用正则表达式匹配表格
    const tableRegex = /\|([^|]+)\|([^|]+)\|([^|]+)\|([^|]+)\|([^|]+)\|([^|]+)\|([^|]+)\|/g;
    const rows = Array.from(content.matchAll(tableRegex));

    // 创建一个对象来存储层级关系和值
    const hierarchy = {};
    let currentCat = '';
    let currentSubCat = '';

    // 遍历表格行
    for (let i = 2; i < rows.length - 1; i++) { // 从第三行开始，跳过表头和分隔行，不处理最后一行
        const [, cat, catVal, subCat, subCatVal, item, itemVal, _] = rows[i].map(cell => cell.trim());

        if (cat) {
            currentCat = cat;
            if (!hierarchy[currentCat]) {
                hierarchy[currentCat] = { value: parseInt(catVal) || 0, subCategories: {} };
            }
        }

        if (subCat) {
            currentSubCat = subCat;
            if (!hierarchy[currentCat].subCategories[currentSubCat]) {
                hierarchy[currentCat].subCategories[currentSubCat] = { value: parseInt(subCatVal) || 0, items: {} };
            }
        }

        if (item) {
            if (!hierarchy[currentCat].subCategories[currentSubCat].items[item]) {
                hierarchy[currentCat].subCategories[currentSubCat].items[item] = parseInt(itemVal) || 0;
            }
        }
    }

    let total = 0
    // 更新值
    for (const cat in hierarchy) {
        let catTotal = 0;
        for (const subCat in hierarchy[cat].subCategories) {
            let subCatTotal = 0
            for (const item in hierarchy[cat].subCategories[subCat].items) {
                // 获取灵石值
                const lingshiVal = getLingshiValue(item.match(/\[\[(.*?)\]\]/)?.[1] || '');
                const currentVal = hierarchy[cat].subCategories[subCat].items[item];
                // 如果灵石值大于当前值，则使用灵石值
                if (lingshiVal > currentVal) {
                    hierarchy[cat].subCategories[subCat].items[item] = lingshiVal;
                }
                subCatTotal += hierarchy[cat].subCategories[subCat].items[item];
            }
            // 只有当子类总和不为0且大于当前值时，才更新分类的值
            if (subCatTotal !== 0 && subCatTotal > hierarchy[cat].subCategories[subCat].value) {
                hierarchy[cat].subCategories[subCat].value = subCatTotal;
            }
            catTotal += hierarchy[cat].subCategories[subCat].value;
        }
        // 只有当子类总和不为0时，才更新分类的值
        if (catTotal !== 0) {
            hierarchy[cat].value = catTotal;
        }
        total += catTotal;
    }

    // 更新表格内容
    let updatedContent = '';
    let lastCat = '';
    let lastSubCat = '';
    let totalLingshi = 0

    for (let i = 0; i < rows.length - 1; i++) {
        let [, cat, catVal, subCat, subCatVal, item, itemVal, _] = rows[i].map(cell => cell.trim());

        if (i < 2) {
            // 保持表头和分隔行不变
            updatedContent += rows[i][0] + '\n';
            continue;
        }

        if (cat) {
            lastCat = cat;
            catVal = hierarchy[lastCat].value;
        }

        if (lastCat && subCat) {
            lastSubCat = subCat;
            subCatVal = hierarchy[lastCat].subCategories[subCat].value;
        }

        if (lastCat && lastSubCat && item) {
            itemVal = hierarchy[lastCat].subCategories[lastSubCat].items[item];
        }

        // 如果值为0，则显示为空字符串
        catVal = !catVal || catVal === 0 ? '' : `*${catVal}*`;
        subCatVal = subCatVal === 0 ? '' : subCatVal;
        itemVal = itemVal === 0 ? '' : itemVal;

        // 获取灵石值
        let lingshiVal = '';
        if (item) {
            const match = item.match(/\[\[(.*?)\]\]/);
            if (match) {
                lingshiVal = getLingshiValue(match[1]);
                totalLingshi += lingshiVal
            }
        }

        updatedContent += `| ${cat} | ${catVal} | ${subCat} | ${subCatVal} | ${item} | ${itemVal} | ${lingshiVal} |\n`;
    }

    // 返回更新后的内容
    return {content:(updatedContent + `| 总计 | *${total}* |  |  | *${total-totalLingshi}* |  | **${totalLingshi}** |\n`).trim() + '\n', fm: {灵石: parseInt(totalLingshi) || 0}};
}

// 新增函数：获取灵石值
function getLingshiValue(itemName) {
    const linkedFile = app.metadataCache.getFirstLinkpathDest(itemName, '');
    if (linkedFile) {
        const frontmatter = app.metadataCache.getFileCache(linkedFile).frontmatter;
        if (frontmatter && frontmatter.灵石) {
            return parseInt(frontmatter.灵石) || 0;
        }
    }
    return 0;
}


module.exports = updateTableValues;
