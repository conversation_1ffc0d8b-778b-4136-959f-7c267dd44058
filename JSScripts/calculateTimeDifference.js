/**
 * 计算时间范围的分钟差
 * @param {string} timeString - 时间范围字符串，格式为 "HH:mm-HH:mm"
 * @returns {number} 时间差（分钟）
 */
function calculateTimeDifference (timeString) {
    if (!timeString) return 0;

    // 标准化时间格式（处理单位数的小时）
    const normalizedTimeString = timeString.replace(/(\d{1}):(\d{2})/g, '$1:$2');
    const times = normalizedTimeString.split('-');

    if (times.length !== 2) return 0;

    const [start, end] = times;
    const timeToMinutes = timeStr => {
        const [hours, minutes] = timeStr.trim().split(':').map(Number);
        return hours * 60 + minutes;
    };
    return timeToMinutes(end) - timeToMinutes(start);
}

module.exports = calculateTimeDifference;