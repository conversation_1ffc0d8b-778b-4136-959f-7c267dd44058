/**
 * 计算文件在当前日记中所获得的灵石
 * <AUTHOR>
 * @version 0.1.1
 * @lastUpdate 2025-02-06
 */

// 配置常量
const CONFIG = {
    DIARY_PATH_PREFIX: "日记/",
    FRONTMATTER_KEY: "灵石",
    FLOW_TYPE_KEY: "流转",
    FLOW_TYPES: {
        DEEP:   ["学习", "实践", "自我实现"],
        ROAM:   ["探索", "兴趣探索"],
        LOOP:   ["练习", "循环"],
        MOTION: ["运动"]
    },
    TIME_THRESHOLDS: {
        DEEP:   [240,420,600],
        ROAM:   [120,180,360],
        LOOP:   [60,120,240],
        MOTION: [60,240,420]
    },
    TIME_BONUS: {
        DEEP:   [1.0, 1.4, 0.6],
        ROAM:   [1.0, 1.3, 0.9],
        LOOP:   [1.0, 1.6, 0.7],
        MOTION: [1.0, 1.0, 1.0]
    }
};

/**
 * 计算灵石
 * @param {number} minutes - 分钟数
 * @param {string} flowType - 流转类型
 * @returns {number} 灵石
 */
function calculateSoulstones (minutes, flowType) {
    // 查找匹配的流转类型
    const flowCategory = Object.entries(CONFIG.FLOW_TYPES)
        .find(([category, types]) => types.includes(flowType))?.[0];

    // 如果找到匹配的类型则计算灵石,否则返回0
    let soulstones = 0;
    if (flowCategory) {
        const timeThresholds = CONFIG.TIME_THRESHOLDS[flowCategory];
        const timeBonus = CONFIG.TIME_BONUS[flowCategory];
        for (let i = 0; i < timeThresholds.length; i++) {
            soulstones += timeBonus[i] * Math.min(minutes, timeThresholds[i]);
            minutes -= timeThresholds[i];
            if (minutes <= 0) break;
        }
        soulstones /= 60.0;
    }

    // 格式化并限制最大值
    return parseFloat(soulstones.toFixed(1));
}

/**
 * @param {TFile} dailyTFile - 日记文件
 * @param {String} targetFileName - 目标文件名
 * @param {String} targetFlowType - 目标流转类型
 * @returns {number} 当日灵石
 */
function main (tp, dailyPath, dailyFileContent, targetFileName, targetFlowType) {
    // 修改正则表达式以匹配新格式：
    // - [任意字符] HH:mm-HH:mm 描述 [[fileName]] 描述
    const taskRegex = new RegExp(
        `- \\[[^\\]]+\\]\\s*([0-9]{1,2}[:：][0-9]{1,2}-[0-9]{1,2}[:：][0-9]{1,2})(?:[^\\[]*?)\\[\\[${targetFileName}(?:\\|[^\\]]*)?\\]\\]`,
        'g'
    );

    let totalMinutes = 0;
    let match;

    while ((match = taskRegex.exec(dailyFileContent)) !== null) {
        const timeString = match[1];
        if (timeString) {
            const normalizedTimeString = timeString.replace(/：/g, ':');
            const minutes = tp.user.calculateTimeDifference(normalizedTimeString);
            totalMinutes += minutes;
        }
    }

    const soulstones = calculateSoulstones(totalMinutes, targetFlowType);
    console.log(`处理文件 ${dailyPath}: 时间 ${totalMinutes} 分钟, 灵石 ${soulstones}`);

    return soulstones;
}

module.exports = main;