function main() {
    // dawn
    const dawn = new Date()
    dawn.setHours(0, 0, 0, 0)
    const dawnStr = dawn.getFullYear() + "-" + (dawn.getMonth() < 9 ? "0" : "") + (dawn.getMonth() + 1) + "-" + (dawn.getDate() < 10 ? "0" : "") + dawn.getDate()

    // cycle
    const cycles = app.vault.getFiles().filter(x=>x.path.startsWith("Nexus/周期/2025/"))
    let cycle = null
    for (const c of cycles) {
        const frontmatter = app.metadataCache.getFileCache(c).frontmatter
        if (!frontmatter || !frontmatter.开始 || !frontmatter.周期排期) {
            continue
        }

        const start = new Date(frontmatter.开始)
        start.setHours(0, 0, 0, 0)
        const count = frontmatter.周期排期.length
        if (dawn.getTime() >= start.getTime() && dawn.getTime() <= (start.getTime() + count * 24 * 60 * 60 * 1000)) {
            cycle = c
            break
        }
    }

    // today
    const today = app.vault.getFileByPath("Nexus/日记/"+dawnStr+".md")

    return {
        today,
        cycle
    }
}
module.exports = main;