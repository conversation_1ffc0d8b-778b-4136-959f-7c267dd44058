/**
 * 更新印记灵石计算脚本
 * <AUTHOR>
 * @description 基于流转类型和时间计算灵石数量
 * @version 0.06
 * @lastUpdate 2025-02-01
 */

// 配置常量
const CONFIG = {
    DIARY_PATH_PREFIX: "日记/",
    FLOW_TYPE_KEY: "流转",
};

/**
 * 计算所有日记所记录的灵石
 * @param {object} tp - Templater插件对象
 * @param {Map} inlinks - 反向链接数据
 * @param {string} currentFileName - 当前文件名
 * @returns {Promise<number>} 总灵石
 */
async function calculateTotalSoulstones(tp, inlinks, currentFileName, flowType) {
    let totalSoulstones = 0.0;

    for (const [sourcePath, linkData] of inlinks) {
        if (!sourcePath.startsWith(CONFIG.DIARY_PATH_PREFIX)) continue;

        const sourceFile = tp.file.find_tfile(sourcePath);
        if (!sourceFile) {
            console.warn(`未找到文件: ${sourcePath}`);
            continue;
        }

        try {
            const fileContent = await tp.app.vault.read(sourceFile);
            const dailySoulstones = tp.user.计算当日灵石(tp, sourcePath, fileContent, currentFileName, flowType)
            totalSoulstones += parseFloat(dailySoulstones);
        } catch (error) {
            console.error(`处理文件 ${sourcePath} 时发生错误: ${error.message}`);
        }
    }

    console.log(`${currentFileName} 总计灵石: ${totalSoulstones}`);
    return parseFloat(totalSoulstones.toFixed(1));
}

/**
 * 更新印记灵石的主函数
 * @param {object} tp - Templater插件对象
 * @param {TFile} currentFile - 当前文件对象
 * @returns {Promise<number>} 计算得到的总灵石数
 */
async function main(tp, currentFile) {
    try {
        const frontmatter = tp.app.metadataCache.getFileCache(currentFile)?.frontmatter;
        const currentFileName = currentFile.basename;

        if (!frontmatter || !frontmatter[CONFIG.FLOW_TYPE_KEY]) {
            console.warn(`文件 ${currentFileName} 缺少流转类型配置`);
            return 0;
        }

        const flowType = frontmatter[CONFIG.FLOW_TYPE_KEY];
        const inlinks = tp.app.metadataCache.getBacklinksForFile(currentFile).data;
        console.log(`开始处理文件 ${currentFileName} 的 ${inlinks.size} 个反向链接`);

        const totalSoulstones = await calculateTotalSoulstones(tp, inlinks, currentFileName, flowType);
        if (totalSoulstones > 0) {
            await tp.app.fileManager.processFrontMatter(currentFile, fm => { fm.灵石 = totalSoulstones; });
        }
        return totalSoulstones;
    } catch (error) {
        console.error(`更新印记灵石时发生错误: ${error.message}`);
        return 0;
    }
}

module.exports = main;