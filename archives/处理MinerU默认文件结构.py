# 适用于拆分章节的书籍
# 文件结构依据是GameAIPro的默认结构
# 内有写死的url 需要手动修改

import os
import shutil
import re
import pandas as pd
from pathlib import Path
import yaml

def extract_chapter_info(folder_name):
    """从文件夹名称中提取章节信息"""
    # 匹配 bookname_Chapter{i}_chaptername 格式
    pattern = r"([^_]+)_Chapter(\d+)_(.+)"
    match = re.match(pattern, folder_name)

    if match:
        bookname, chapter_num, chapter_name = match.groups()
        return bookname, int(chapter_num), chapter_name
    else:
        # 没有Chapter格式的情况
        parts = folder_name.split('_', 1)
        if len(parts) == 2:
            return parts[0], 0, parts[1]
        return folder_name, 0, folder_name

def process_markdown(md_path, chapter_info, old_folder_name):
    """处理markdown文件，添加frontmatter"""
    bookname, _, chapter_name = chapter_info

    # 创建frontmatter数据
    frontmatter = {
        'title': chapter_name.replace('_', ' '),
        'subtitle': '',
        'author': '',
        'publish_time': '',
        'url': f"http://www.gameaipro.com/{bookname}/{old_folder_name}.pdf",
        'tags': [],
        'cover_img': '',
        'language': 'en',
        'book': bookname
    }

    # 读取原始内容
    with open(md_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 组合新内容
    yaml_content = '\n'.join([f"{k}: {v}" for k, v in frontmatter.items()])
    new_content = f"---\n{yaml_content}\n---\n\n{content}"

    # 写入文件
    with open(md_path, 'w', encoding='utf-8') as f:
        f.write(new_content)

def update_csv(csv_path, folder_info, old_folder_name):
    """更新CSV文件信息"""
    try:
        df = pd.read_csv(csv_path)
        bookname, _, _ = folder_info

        # 更新或添加记录
        url = f"http://www.gameaipro.com/{bookname}/{old_folder_name}.pdf"
        new_row = {
            'url': url,
            'status': 'success',
            'update_time': pd.Timestamp.now()
        }

        # 检查URL是否存在，存在则更新，不存在则添加
        if url in df['url'].values:
            df.loc[df['url'] == url, ['status', 'update_time']] = [
                new_row['status'], new_row['update_time']]
        else:
            df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)

        df.to_csv(csv_path, index=False)
    except Exception as e:
        print(f"更新CSV文件时出错: {e}")

def process_folder(root_path, csv_path):
    """处理主文件夹"""
    root_path = Path(root_path)

    for folder in root_path.iterdir():
        if not folder.is_dir():
            continue

        # 提取章节信息
        old_folder_name = folder.name
        chapter_info = extract_chapter_info(old_folder_name)
        bookname, chapter_num, chapter_name = chapter_info

        # 创建新的文件夹名
        new_folder_name = f"ch{chapter_num}-{chapter_name}"
        new_folder_path = folder.parent / new_folder_name

        try:
            # 创建新文件夹
            new_folder_path.mkdir(exist_ok=True)

            auto_folder = folder / 'auto'
            if auto_folder.exists():
                # 移动并重命名文件
                for file in auto_folder.iterdir():
                    if file.name.endswith('_origin.pdf'):
                        shutil.copy2(file, new_folder_path / 'origin.pdf')
                    elif file.name.endswith('.md'):
                        new_md_path = new_folder_path / 'doc.md'
                        shutil.copy2(file, new_md_path)
                        process_markdown(new_md_path, chapter_info, old_folder_name)

                # 移动images文件夹
                old_images = auto_folder / 'images'
                if old_images.exists():
                    shutil.copytree(old_images, new_folder_path / 'images', dirs_exist_ok=True)

            # 更新CSV
            update_csv(csv_path, chapter_info, old_folder_name)

            # 删除原始文件夹
            shutil.rmtree(folder)

            print(f"成功处理文件夹: {folder.name}")

        except Exception as e:
            print(f"处理文件夹 {folder.name} 时出错: {e}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) != 3:
        print("使用方法: python process_book_folders.py <书籍目录路径> <CSV文件路径>")
        sys.exit(1)

    book_path = sys.argv[1]
    csv_path = sys.argv[2]

    if not os.path.exists(book_path):
        print(f"错误: 目录 {book_path} 不存在")
        sys.exit(1)

    if not os.path.exists(csv_path):
        print(f"错误: CSV文件 {csv_path} 不存在")
        sys.exit(1)

    process_folder(book_path, csv_path)