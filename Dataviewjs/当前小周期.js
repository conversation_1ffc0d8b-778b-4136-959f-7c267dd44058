/*
 * @作者: roam1n
 * @版本: 0.0.5
 * @最后更新: 2025-04-06
 */

// 工具函数：获取当前小周期
function getCurrentMicroCycle(today) {
    return input?.micro ? dv.page(input?.micro) : dv.pages(`"Nexus/周期"`)
        .sort(p => p.file.name)
        .find(p => {
            const start = new Date(p.开始);
            const count = p.周期排期?.length * p.周期循环;
            return p.周期排期 &&
                    today.getTime() >= start.getTime() && 
                    today.getTime() <= (start.getTime() + count * 24 * 60 * 60 * 1000);
        });
}

// 初始化数据
const TODAY = new Date();
TODAY.setHours(0, 0, 0, 0);
const MICRO = getCurrentMicroCycle(TODAY);
if (!MICRO) {
    dv.paragraph("未设置当前周期");
    return;
}

const MICRO_SIZE = MICRO.周期排期.length * MICRO.周期循环;
const MICRO_START = new Date(MICRO.开始);
MICRO_START.setHours(0, 0, 0, 0);
const DAILIES = dv.pages(`"Nexus/日记"`).filter(p=>{
    const date = new Date(p.file.name.replace('.md', ''));
    return date.getTime() >= MICRO_START.getTime() && date.getTime() <= (MICRO_START.getTime() + MICRO_SIZE * 24 * 60 * 60 * 1000);
});
const TASK_TYPES = ['C', 'Z', 'H', 'Q', 'S'];

// 统计日记中的印记文件
const steps = {};
DAILIES.forEach(daily => {
    daily.file.tasks.forEach(task => {
        // 去除前后空格
        const text = task.text.trim();
        
        // 匹配时间、描述和文件名的正则
        // 时间格式: HH:MM-HH:MM 或 HH：MM-HH：MM
        // 文件格式: [[文件名]]
        const pattern = /^(\d{2}[:：]\d{2})\s*-\s*(\d{2}[:：]\d{2})(?:\s+([^[]+))?\s*\[\[(.*?)\]\]|^(\d{2}[:：]\d{2})\s*-\s*(\d{2}[:：]\d{2})\s*\[\[(.*?)\]\](?:\s+(.*))?$/;
        
        const match = text.match(pattern);
        if (match) {
            // 根据匹配位置确定时间和文件名
            let startTime, endTime, fileName;
            if (match[1] !== undefined) {
                // 文件名在后模式
                startTime = match[1].replace('：', ':');
                endTime = match[2].replace('：', ':');
                fileName = match[4];
            } else {
                // 文件名在前模式
                startTime = match[5].replace('：', ':');
                endTime = match[6].replace('：', ':');
                fileName = match[7];
            }

            // 计算时间差（分钟）
            const [startHour, startMin] = startTime.split(':').map(Number);
            const [endHour, endMin] = endTime.split(':').map(Number);
            const duration = (endHour * 60 + endMin) - (startHour * 60 + startMin);

            // 更新steps
            if (fileName in steps) {
                steps[fileName].time += duration;
            } else {
                const file = dv.page(fileName);
                steps[fileName] = {time: duration, daily: daily.file.name,
                    tasks: file.file.tasks.filter(task => TASK_TYPES.includes(task.status)), 
                    journeyName: file.旅程?.path?.split('/')?.last()?.replace('.md', '')};
            }
        }
    });
});

// 创建容器
const container = dv.container;
container.style.width = "100%";
container.style.height = "100%";
container.style.position = "relative";
container.style.backgroundColor = "var(--background-primary)";
container.style.marginBottom = "12px";

// 在容器样式设置部分添加动画定义
const style = document.createElement('style');
style.textContent = `
    @keyframes perfect-pulse {
        0% {
            transform: scale(1);
            filter: brightness(1);
        }
        25% {
            transform: scale(1.05);
            filter: brightness(1.15) contrast(1.05);
        }
        60% {
            transform: scale(1.05);
            filter: brightness(1.15) contrast(1.05);
        }
        80% {
            transform: scale(0.95);
            filter: brightness(0.85) contrast(0.95);
        }
        100% {
            transform: scale(1);
            filter: brightness(1);
        }
    }
`;
document.head.appendChild(style);

// 😈 当前小周期
const cycleContainer = document.createElement("div");
cycleContainer.style.width = "100%";
cycleContainer.style.height = "100%";
cycleContainer.style.position = "relative";
cycleContainer.style.borderRadius = "5px";
cycleContainer.style.display = "flex";
cycleContainer.style.flexDirection = "column";
cycleContainer.style.gap = "12px";
cycleContainer.style.padding = "12px";
container.appendChild(cycleContainer);

// 标题区域
const titleSection = document.createElement("div");
titleSection.style.display = "flex";
titleSection.style.justifyContent = "space-between";
titleSection.style.alignItems = "center";
titleSection.style.borderRadius = "4px";
titleSection.style.paddingBottom = "6px";
titleSection.style.borderBottom = "1px solid var(--background-modifier-border)";
cycleContainer.appendChild(titleSection);

// 周期标题
const cycleTitle = document.createElement("a");
const cycle = MICRO.file.name.split('=')[1];
cycleTitle.href = MICRO.file.name;
cycleTitle.className = "internal-link";
cycleTitle.dataset['href'] = MICRO.file.name;
cycleTitle.textContent = `第${cycle}周期`;
cycleTitle.style.fontWeight = "600";
cycleTitle.style.color = "var(--text-normal)";
cycleTitle.style.opacity = "0.9";
cycleTitle.style.fontSize = "0.9em";
titleSection.appendChild(cycleTitle);

// 添加统计信息
const statsInfo = document.createElement("div");
statsInfo.textContent = `共${MICRO_SIZE}天`;
statsInfo.style.fontSize = "0.7em";
statsInfo.style.color = "var(--text-muted)";
statsInfo.style.opacity = "0.8";
titleSection.appendChild(statsInfo);

// 日期正方形网格
const daysGrid = document.createElement("div");
daysGrid.style.display = "grid";
daysGrid.style.width = "100%";
daysGrid.style.gap = "2px";
daysGrid.style.gridTemplateColumns = `repeat(${MICRO_SIZE}, 1fr)`;
daysGrid.style.padding = "0 2px";
cycleContainer.appendChild(daysGrid);

// 颜色配置
const CYCLE_COLORS = {
    'A': '#8B6BC7',
    'B': '#8B6BC7',
    'C': '#65A849',
    'D': '#65A849',
    'E': '#3DA6A6',
    'F': '#E5B567',
    'G': '#E57373'
};

// 获取日期对应的颜色
function getMicroColor(index) {
    const microType = MICRO.周期排期?.[index % MICRO.周期排期.length] || '';
    return CYCLE_COLORS[microType] || '#888888';
}

// 创建日期方格
for (let i = 0; i < MICRO_SIZE; i++) {
    const date = new Date(MICRO_START);
    date.setDate(date.getDate() + i);
    date.setHours(0, 0, 0, 0);
    const dayStr = date.getFullYear() + '-' + (date.getMonth() < 9 ? '0' : '') + (date.getMonth() + 1) + '-' + (date.getDate() < 10 ? '0' : '') + date.getDate();
    const dailyPage = DAILIES.find(p => p.file.name === dayStr);
    
    const daySquare = document.createElement("div");
    daySquare.style.aspectRatio = "1/1";
    daySquare.style.display = "flex";
    daySquare.style.alignItems = "center";
    daySquare.style.justifyContent = "center";
    daySquare.style.borderRadius = "4px";
    daySquare.style.cursor = "pointer";
    daySquare.style.transition = "all 0.2s ease";
    daySquare.style.position = "relative";
    
    // 日期链接
    const dayLink = document.createElement("a");
    dayLink.href = `Nexus/日记/${dayStr}`;
    dayLink.className = "internal-link";
    dayLink.dataset['href'] = `Nexus/日记/${dayStr}`;
    dayLink.style.width = "100%";
    dayLink.style.height = "100%";
    dayLink.style.display = "flex";
    dayLink.style.flexDirection = "column-reverse";
    dayLink.style.alignItems = "center";
    dayLink.style.justifyContent = "center";
    dayLink.style.textDecoration = "none";
    
    // 基于日期状态应用样式
    const microColor = getMicroColor(i);

    if (!dailyPage) {
        // 类型1: 不存在的日记文件 - 灰色
        daySquare.style.backgroundColor = "rgba(100, 100, 100, 0.2)";
        daySquare.style.color = "rgba(150, 150, 150, 0.5)";
        // 显示日期
        const day = date.getDate().toString().padStart(2, '0');
        dayLink.textContent = day;
        dayLink.style.color = `${microColor}20`;
        dayLink.style.fontSize = "1em";
    } else {
        // 检查是否有状态为Y的任务
        const hasCompletedTask = dailyPage.file.tasks.some(task => task.status === 'Y');

        daySquare.style.backgroundColor = `${microColor}80`;
        daySquare.style.border = `1px solid ${microColor}E6`;
        dayLink.style.color = `${microColor}E6`;
        dayLink.style.position = "relative";

        if (hasCompletedTask) {
            // 类型2: 有已完成任务的日记 - 显示图标
            dayLink.innerHTML = `<div style="font-size: 8px; font-weight: 200; line-height: 1;">${date.getDate()}</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-crown"><path d="M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z"/><path d="M5 21h14"/></svg>`;
        } else {
            // 类型3: 普通日记 - 显示日期
            const day = date.getDate().toString().padStart(2, '0');
            dayLink.textContent = day;
            dayLink.style.fontSize = "1em";
            dayLink.style.fontWeight = "bold";
        }
    }

    // 如果是今天
    if (date.toISOString().split('T')[0] === TODAY.toISOString().split('T')[0]) {
        dayLink.style.fontWeight = "bold";
        daySquare.style.transform = "scale(1.05)";
        daySquare.style.boxShadow = "0 0 8px var(--background-modifier-border)";
        daySquare.style.animation = "perfect-pulse 2s infinite";
    }

    // 如果是周一
    if (date.getDay() === 1) {
        const dayInfo = document.createElement("div");
        dayInfo.textContent = "*";
        dayInfo.style.fontSize = "0.5vw";
        dayInfo.style.lineHeight = "1";
        dayInfo.style.color = `${microColor}20`;
        // dayInfo.style.position = "absolute";
        // dayInfo.style.top = "5px";
        // dayInfo.style.right = "calc(50% - 3.5px)";
        dayLink.appendChild(dayInfo);
        dayLink.style.fontSize = "0.8em";
        if (dailyPage) {
            dayInfo.style.color = `${microColor}E6`;
        }
    }
    
    // Hover效果
    daySquare.addEventListener("mouseenter", () => {
        if (dailyPage) {
            daySquare.style.backgroundColor = `${microColor}B3`;
            daySquare.style.transform = "scale(1.05)";
        }
    });
    
    daySquare.addEventListener("mouseleave", () => {
        if (dailyPage) {
            daySquare.style.backgroundColor = `${microColor}80`;
            daySquare.style.transform = "scale(1)";
        }
    });

    
    daySquare.appendChild(dayLink);
    daysGrid.appendChild(daySquare);
}

// 印记部分
const imprintsSection = document.createElement("div");
imprintsSection.style.display = "flex";
imprintsSection.style.flexDirection = "column";
imprintsSection.style.gap = "8px";
imprintsSection.style.marginTop = "8px";
imprintsSection.style.backgroundColor = "var(--background-secondary)";
imprintsSection.style.borderRadius = "4px";
imprintsSection.style.padding = "10px";
cycleContainer.appendChild(imprintsSection);

// 旅程类别配置
const JOURNEY_CATEGORIES = {
    'lx': MICRO.练习?.path?.split('/')?.last()?.replace('.md', ''),
    'xx': MICRO.学习?.path?.split('/')?.last()?.replace('.md', ''),
    'sj': MICRO.实践?.path?.split('/')?.last()?.replace('.md', ''),
    'tz': MICRO.拓展?.path?.split('/')?.last()?.replace('.md', ''),
    'yl': MICRO.娱乐?.path?.split('/')?.last()?.replace('.md', ''),
    'xh': MICRO.循环?.path?.split('/')?.last()?.replace('.md', '')
};

// 获取旅程类别
function getJourneyCategory(journeyName) {
    if (!journeyName) return 'ot';
    
    for (const [category, name] of Object.entries(JOURNEY_CATEGORIES)) {
        if (name === journeyName) return category;
    }
    
    return 'ot';
}

// 里程碑图标
const getMilestoneIcon = (status) => {
    // 预留位置给不同类型的图标
    switch(status) {
        case 'C':
            return `<div data-icon='C' style="width: 16px; height: 16px; background-color: var(--color-task-${status}); border-radius: 50%; display: flex; align-items: center; justify-content: center;"></div>`;
        case 'Z':
            return `<div data-icon='Z' style="width: 16px; height: 16px; background-color: var(--color-task-${status}); border-radius: 50%; display: flex; align-items: center; justify-content: center;"></div>`;
        case 'H':
            return `<div data-icon='H' style="width: 16px; height: 16px; background-color: var(--color-task-${status}); border-radius: 50%; display: flex; align-items: center; justify-content: center;"></div>`;
        case 'Q':
            return `<div data-icon='Q' style="width: 16px; height: 16px; background-color: var(--color-task-${status}); border-radius: 50%; display: flex; align-items: center; justify-content: center;"></div>`;
        case 'S':
            return `<div data-icon='S' style="width: 16px; height: 16px; background-color: var(--color-task-${status}); border-radius: 50%; display: flex; align-items: center; justify-content: center;"></div>`;
        default:
            return '';
    }
};

// 旅程图标（预留位置）
const getJourneyIcon = (category) => {
    // 预留SVG图标位置，稍后可以替换为实际图标
    const colorMap = {
        'yl': '#E57373', // 浅红色
        'sj': '#64B5F6', // 浅蓝色
        'xx': '#81C784', // 浅绿色
        'tz': '#9575CD', // 浅紫色
        'lx': '#FFB74D', // 橙色
        'xh': '#4DB6AC', // 青色
        'ot': '#A1887F'  // 棕灰色
    };

    const color = colorMap[category] || colorMap['ot'];
    
    return `<div style="width: 24px; height: 24px; background-color: ${color}; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 12px;">${category.toUpperCase()}</div>`;
};

// 显示印记数据
const imprintsTitle = document.createElement("div");
imprintsTitle.textContent = "印记 " + Object.values(steps).reduce((acc, step) => acc + step.time, 0);
imprintsTitle.style.fontWeight = "bold";
imprintsTitle.style.marginBottom = "6px";
imprintsTitle.style.color = "var(--text-normal)";
imprintsTitle.style.opacity = "0.9";
imprintsTitle.style.fontSize = "0.8em";
imprintsSection.appendChild(imprintsTitle);

// 创建印记列表
const imprintsList = document.createElement("div");
imprintsList.style.display = "flex";
imprintsList.style.flexDirection = "column";
imprintsList.style.gap = "8px";
imprintsSection.appendChild(imprintsList);

// 处理印记数据
Object.entries(steps).forEach(([fileName, stepData]) => {
    const imprintItem = document.createElement("div");
    imprintItem.style.display = "flex";
    imprintItem.style.gap = "4px";
    imprintItem.style.backgroundColor = "var(--background-primary)";
    imprintItem.style.borderRadius = "4px";
    imprintItem.style.padding = "8px";
    imprintItem.style.transition = "all 0.2s ease";

    // Hover效果
    imprintItem.addEventListener("mouseenter", () => {
        imprintItem.style.backgroundColor = "var(--background-primary-alt)";
        imprintItem.style.transform = "translateX(2px)";
    });
    
    imprintItem.addEventListener("mouseleave", () => {
        imprintItem.style.backgroundColor = "var(--background-primary)";
        imprintItem.style.transform = "translateX(0)";
    });
    
    // 印记标题行
    const titleRow = document.createElement("div");
    titleRow.style.display = "flex";
    titleRow.style.alignItems = "center";
    titleRow.style.gap = "10px";
    titleRow.style.flex = "1";
    
    // 旅程图标
    const journeyCategory = getJourneyCategory(stepData.journeyName);
    const iconDiv = document.createElement("div");
    iconDiv.innerHTML = getJourneyIcon(journeyCategory);
    titleRow.appendChild(iconDiv);
    
    // 时间统计（四位数显示）
    const timeDiv = document.createElement("div");
    timeDiv.textContent = stepData.time.toString().padStart(4, '0');
    timeDiv.style.backgroundColor = "var(--background-secondary)";
    timeDiv.style.padding = "2px 6px";
    timeDiv.style.borderRadius = "4px";
    timeDiv.style.fontSize = "0.85em";
    timeDiv.style.color = "var(--text-muted)";
    titleRow.appendChild(timeDiv);
    
    // 印记文件链接
    const fileLink = document.createElement("a");
    fileLink.href = fileName;
    fileLink.className = "internal-link";
    fileLink.dataset['href'] = fileName;
    fileLink.textContent = fileName.split('/').pop();
    fileLink.style.color = "var(--text-accent)";
    fileLink.style.textDecoration = "none";
    fileLink.style.fontWeight = "500";
    fileLink.style.flex = "1";
    titleRow.appendChild(fileLink);
    
    imprintItem.appendChild(titleRow);
    
    // 里程碑
    if (stepData.tasks && stepData.tasks.length > 0) {
        const milestonesContainer = document.createElement("div");
        milestonesContainer.style.marginLeft = "24px";
        milestonesContainer.style.marginTop = "4px";
        milestonesContainer.style.display = "flex";
        milestonesContainer.style.flexDirection = "column";
        milestonesContainer.style.gap = "4px";
        milestonesContainer.style.flex = "1";
        
        stepData.tasks.forEach(task => {
            // 过滤掉之前日期的任务
            const match = task.text.match(/\[\[(\d{4})-(\d{2})-(\d{2})\]\]/g);
            const taskDate = match ? new Date(match[0].replace(/\[\[|\]\]/g, '')) : null;
            if (taskDate && taskDate.getTime() < MICRO_START.getTime()) {
                return;
            }

            if (TASK_TYPES.includes(task.status)) {
                const milestone = document.createElement("div");
                milestone.style.display = "flex";
                milestone.style.alignItems = "center";
                milestone.style.gap = "8px";
                milestone.style.fontSize = "0.9em";
                milestone.style.color = "var(--text-normal)";
                milestone.style.opacity = "0.85";
                
                // 里程碑图标
                const milestoneIconDiv = document.createElement("div");
                milestoneIconDiv.innerHTML = getMilestoneIcon(task.status);
                milestone.appendChild(milestoneIconDiv);
                
                // 里程碑描述
                const descDiv = document.createElement("div");
                descDiv.textContent = task.text.replace(/\[\[(\d{4})-(\d{2})-(\d{2})\]\]/g, '$2-$3');
                descDiv.style.flex = "1";
                milestone.appendChild(descDiv);
                
                milestonesContainer.appendChild(milestone);
            }
        });
        
        imprintItem.appendChild(milestonesContainer);
    }
    
    imprintsList.appendChild(imprintItem);
});

// 响应式调整
const adjustStyles = () => {
    const containerWidth = container.offsetWidth;
    
    // 调整字体大小
    const baseFontSize = Math.max(10, Math.min(16, containerWidth / 50));
    imprintsSection.style.fontSize = `${baseFontSize}px`;
};

// 初始调整
adjustStyles();

// 监听窗口大小变化
window.addEventListener('resize', adjustStyles);
