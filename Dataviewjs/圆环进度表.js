/*
 * @描述: 圆环形式展示目标进度
 * @作者: Claude
 * @版本: 0.0.3
 */

// 创建主容器
const container = dv.container;
container.style.width = "100%";
container.style.display = "flex";
container.style.flexWrap = "wrap";
container.style.gap = "8px";
container.style.padding = "4px";
container.style.position = "relative";

// 创建提示框元素
document.querySelectorAll('.ring-tooltip').forEach(el => el.remove());
const tooltip = document.body.createEl("div", { attr: { class: "ring-tooltip" } });
Object.assign(tooltip.style, {
    position: "fixed",
    backgroundColor: "var(--background-primary)",
    color: "var(--text-normal)",
    padding: "4px 8px",
    borderRadius: "4px",
    fontSize: "12px",
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.2)",
    border: "1px solid var(--background-modifier-border)",
    zIndex: "999999",
    display: "none",
    pointerEvents: "none",
    maxWidth: "200px",
    wordWrap: "break-word",
    transform: "translate(-50%, -100%)",
    marginTop: "-8px",
});

// 颜色方案
const colorSchemes = [
    // 青色系
    ["#00ACC1", "#26C6DA", "#80DEEA"],
    // 蓝色系
    ["#1E88E5", "#42A5F5", "#90CAF9"],
    // 紫色系
    ["#8E24AA", "#AB47BC", "#CE93D8"],
    // 红色系
    ["#E53935", "#EF5350", "#EF9A9A"],
    // 橙色系
    ["#FB8C00", "#FFA726", "#FFCC80"],
    // 绿色系
    ["#43A047", "#66BB6A", "#A5D6A7"]
];

// 获取目标数据
const targets = input?.targets || [];

// 创建SVG命名空间
const svgNS = "http://www.w3.org/2000/svg";

// 计算进度圆环
function calculateProgress(start, target, current) {
    const normalizedCurrent = Math.max(start, Math.min(current, 1));
    const normalizedTarget = Math.max(start, Math.min(target, 1));
    const progress = (normalizedCurrent - start) / (normalizedTarget - start);
    return progress * 100; // 返回百分比进度
}

// 获取进度对应的颜色
function getProgressColors(progress, colorScheme) {
    if (progress <= 100) {
        return {
            base: "var(--background-secondary)",
            progress: colorScheme[0]
        };
    } else if (progress <= 200) {
        return {
            base: colorScheme[0],
            progress: colorScheme[1]
        };
    } else {
        return {
            base: colorScheme[1],
            progress: colorScheme[2]
        };
    }
}

// 创建圆环
function createRing(container, data, index) {
    const size = data.focus ? 80 : 60;
    const strokeWidth = data.focus ? 6 : 4;
    const radius = (size - strokeWidth) / 2;
    const center = size / 2;

    const card = container.createEl("div");
    Object.assign(card.style, {
        width: `${size}px`,
        height: `${size}px`,
        position: "relative",
        margin: "4px",
        borderRadius: "50%",
        backgroundColor: "var(--background-secondary)",
        cursor: "pointer",
        opacity: "0.5",
    });

    const svg = document.createElementNS(svgNS, "svg");
    svg.setAttribute("width", size);
    svg.setAttribute("height", size);
    svg.setAttribute("viewBox", `0 0 ${size} ${size}`);
    svg.style.zIndex = "99";
    card.appendChild(svg);

    // 计算进度
    const progress = calculateProgress(data.start, data.target, data.current);
    const colorScheme = colorSchemes[index % colorSchemes.length];
    const colors = getProgressColors(progress, colorScheme);

    // 创建底色圆环
    const baseCircle = document.createElementNS(svgNS, "circle");
    baseCircle.setAttribute("cx", center);
    baseCircle.setAttribute("cy", center);
    baseCircle.setAttribute("r", radius);
    baseCircle.setAttribute("fill", "none");
    baseCircle.setAttribute("stroke", colors.base);
    baseCircle.setAttribute("stroke-width", strokeWidth);
    svg.appendChild(baseCircle);

    // 绘制进度圆环
    if (progress > 0) {
        const progressArc = document.createElementNS(svgNS, "path");
        const startAngle = -45; // 从左上角开始
        const progressAngle = (progress % 100) * 3.6; // 将百分比转换为角度
        const endAngle = startAngle + (progressAngle === 360 ? 359.9 : progressAngle); // 处理100%的情况

        const start = polarToCartesian(center, center, radius, startAngle);
        const end = polarToCartesian(center, center, radius, endAngle);

        const largeArcFlag = (progress % 100) >= 50 ? 1 : 0;

        const d = [
            "M", start.x, start.y,
            "A", radius, radius, 0, largeArcFlag, 1, end.x, end.y
        ].join(" ");

        progressArc.setAttribute("d", d);
        progressArc.setAttribute("fill", "none");
        progressArc.setAttribute("stroke", colors.progress);
        progressArc.setAttribute("stroke-width", strokeWidth);
        svg.appendChild(progressArc);

        // 如果进度是100%的倍数，添加一个完整的圆环
        if (progress % 100 === 0 && progress > 0) {
            const fullCircle = document.createElementNS(svgNS, "circle");
            fullCircle.setAttribute("cx", center);
            fullCircle.setAttribute("cy", center);
            fullCircle.setAttribute("r", radius);
            fullCircle.setAttribute("fill", "none");
            fullCircle.setAttribute("stroke", colors.progress);
            fullCircle.setAttribute("stroke-width", strokeWidth);
            svg.appendChild(fullCircle);
        }
    }

    // 添加进度文本
    const textElement = card.createEl("div");
    textElement.textContent = `${Math.round(progress)}%`;
    Object.assign(textElement.style, {
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        fontSize: data.focus ? "14px" : "10px",
        fontWeight: "500",
        color: colors.progress,
    });

    // 添加提示信息
    card.addEventListener("mousemove", (e) => {
        tooltip.style.display = "block";
        tooltip.textContent = `${data.text} (${Math.round(progress)}%)`;
        tooltip.style.left = `${e.clientX}px`;
        tooltip.style.top = `${e.clientY}px`;
        card.style.opacity = "0.7";
    });

    card.addEventListener("mouseleave", () => {
        tooltip.style.display = "none";
        card.style.opacity = "0.5";
    });
}

// 辅助函数：极坐标转笛卡尔坐标
function polarToCartesian(centerX, centerY, radius, angleInDegrees) {
    const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
    return {
        x: centerX + (radius * Math.cos(angleInRadians)),
        y: centerY + (radius * Math.sin(angleInRadians))
    };
}

// 渲染所有进度圆环
if (targets.length > 0) {
    // 先渲染重要的目标
    targets.filter(t => t.focus).forEach((target, index) => {
        createRing(container, target, index);
    });

    // 再渲染不重要的目标
    targets.filter(t => !t.focus).forEach((target, index) => {
        createRing(container, target, index + targets.filter(t => t.focus).length);
    });
} else {
    const message = container.createEl("div");
    Object.assign(message.style, {
        textAlign: "center",
        color: "var(--text-muted)",
        padding: "20px",
        width: "100%",
    });
    message.textContent = "没有找到目标数据";
}