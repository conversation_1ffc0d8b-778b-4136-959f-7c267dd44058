/*
 * @作者: roam1n
 * @版本: 0.0.1
 * @最后更新: 2025-05-22
 */

// 获取输入参数
const folders = input?.folders || ["Nexus"];
const classify = input?.classify || "up";

// 获取指定文件夹下的所有页面
const allPages = folders.reduce((acc, folder) => {
    const pages = dv.pages(`"${folder}"`)
        .filter(p=>p[classify])
        .sort(p=>Array.isArray(p[classify]) ? p[classify][0]?.path : p[classify]?.path, 'asc');
    return [...acc, ...pages];
}, []);

// 创建容器
const container = dv.container;
container.style.margin = "2px auto";
container.style.position = "relative";
container.style.fontSize = "14px";

// 添加样式
const style = document.createElement('style');
style.textContent = `
    .cm-contentContainer.cm-contentContainer>.cm-content>div:has(.classify-panel) {
        max-width: 50rem !important;
        width: 90% !important;
    }
    .classify-item:hover {
        background-color: var(--background-secondary-alt);
        transform: translateX(2px);
    }
    .classify-title:hover {
        color: var(--text-accent);
    }
    .classify-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 12px;
        width: 100%;
    }
    @media (min-width: 1200px) {
        .classify-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    @media (max-width: 1199px) and (min-width: 768px) {
        .classify-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    @media (max-width: 767px) {
        .classify-grid {
            grid-template-columns: 1fr;
        }
    }`;
container.appendChild(style);

// 创建主面板
const mainPanel = document.createElement("div");
mainPanel.className = 'classify-panel';
mainPanel.style.width = "100%";
mainPanel.style.height = "100%";
mainPanel.style.position = "relative";
mainPanel.style.borderRadius = "5px";
mainPanel.style.display = "flex";
mainPanel.style.flexDirection = "column";
mainPanel.style.gap = "12px";
mainPanel.style.padding = "12px";
container.appendChild(mainPanel);

// 标题区域
const titleSection = document.createElement("div");
titleSection.style.display = "flex";
titleSection.style.justifyContent = "space-between";
titleSection.style.alignItems = "center";
titleSection.style.borderRadius = "4px";
titleSection.style.paddingBottom = "6px";
titleSection.style.borderBottom = "1px solid var(--background-modifier-border)";
mainPanel.appendChild(titleSection);

// 面板标题
const panelTitle = document.createElement("div");
panelTitle.textContent = "分类列表";
panelTitle.style.fontWeight = "600";
panelTitle.style.color = "var(--text-normal)";
panelTitle.style.opacity = "0.9";
panelTitle.style.fontSize = "0.9em";
titleSection.appendChild(panelTitle);

// 添加统计信息
const statsInfo = document.createElement("div");
statsInfo.textContent = `共${allPages.length}个页面`;
statsInfo.style.fontSize = "0.7em";
statsInfo.style.color = "var(--text-muted)";
statsInfo.style.opacity = "0.8";
titleSection.appendChild(statsInfo);

function getClassifyName(param) {
    if (typeof param === "string") {
        return param
    } else {
        if (!param.path) { debugger}
        return param.path.split("/").pop().replace(".md", "");
    }
}

// 将页面进行分类
const classifyList = allPages.reduce((acc, page) => {
    if (Array.isArray(page[classify])) {
        page[classify].forEach(item => {
            const classifyName = getClassifyName(item);
            if (!acc[classifyName]) {
                acc[classifyName] = [];
            }
            acc[classifyName].push(page);
        });
    } else {
        const classifyName = getClassifyName(page[classify]);
        if (!acc[classifyName]) {
            acc[classifyName] = [];
        }
        acc[classifyName].push(page);
    }
    return acc;
}, {});

// 将 classifyList 转换为数组并排序
const sortedClassifyList = Object.entries(classifyList)
    .sort(([, a], [, b]) => b.length - a.length)
    .reduce((acc, [key, value]) => {
        acc[key] = value;
        return acc;
    }, {});

// 创建分类网格容器
const classifyGrid = document.createElement("div");
classifyGrid.className = "classify-grid";
mainPanel.appendChild(classifyGrid);

// 渲染分类列表
Object.entries(sortedClassifyList).forEach(([key, value]) => {
    const classifyContainer = document.createElement("div");
    classifyGrid.appendChild(classifyContainer);
    classifyContainer.style.backgroundColor = "var(--background-secondary)";
    classifyContainer.style.borderRadius = "4px";
    classifyContainer.style.padding = "10px";

    const classifyTitle = document.createElement("div");
    classifyContainer.appendChild(classifyTitle);
    classifyTitle.className = "classify-title";
    classifyTitle.style.fontSize = "0.85em";
    classifyTitle.style.fontWeight = "500";
    classifyTitle.style.color = "var(--text-normal)";
    classifyTitle.style.marginBottom = "8px";
    classifyTitle.style.padding = "4px 8px";
    classifyTitle.style.borderRadius = "4px";
    classifyTitle.style.transition = "all 0.2s ease";
    classifyTitle.textContent = key;

    const classifyContent = document.createElement("div");
    classifyContainer.appendChild(classifyContent);
    classifyContent.style.display = "flex";
    classifyContent.style.flexWrap = "wrap";
    classifyContent.style.gap = "8px";

    value.forEach(page => {
        const classifyItem = document.createElement("a");
        classifyContent.appendChild(classifyItem);
        classifyItem.className = "classify-item";
        classifyItem.href = page.file.path;
        classifyItem.classList.add("internal-link");
        classifyItem.textContent = page.file.name;
        classifyItem.style.fontSize = "0.8em";
        classifyItem.style.color = "var(--text-muted)";
        classifyItem.style.padding = "4px 8px";
        classifyItem.style.backgroundColor = "var(--background-primary)";
        classifyItem.style.borderRadius = "4px";
        classifyItem.style.transition = "all 0.2s ease";
        classifyItem.style.textDecoration = "none";
    });
});