/*
 * @描述: 阶段进度表
 * @作者: roam1n
 * @版本: 0.0.4
 * @最后更新: 2025-03-11
 */

/* Input
 * @param {object} input - 输入参数对象
 * @param {string} input.start - 阶段开始时间（日期） '2024-11-23'
 * @param {string} input.template - 第一位是循环次数；第二位单次循环A天数；第三位单次循环B天数；第四位是C天数（C不进入循环，且总是最后一天）
 * @param {string} input.skip_days - 跳过日期，格式为[YYYY-MM-DD,YYYY-MM-DD]
 * @param {object} input.targets - 目标值配置
 * @param {object} input.targets.A - A日目标值 {focus: number, free: number, sport: number}
 * @param {object} input.targets.B - B日目标值 {focus: number, free: number, sport: number}
 * @param {object} input.targets.C - C日目标值 {focus: number, free: number, sport: number}
 */

// 常量定义
const PERFECT_TARGETS = {
    A: { focus: 540, sport: 75, free: 45 },
    B: { focus: 420, sport: 90, free: 90 },
    C: { focus: 180, sport: 75, free: 165 },
    D: { focus: 180, sport: 30, free: 60 }
};

const DEFAULT_TARGETS = {
    A: {
        focus: Math.floor(PERFECT_TARGETS.A.focus / 2),
        free: Math.floor(PERFECT_TARGETS.A.free / 2),
        sport: Math.floor(PERFECT_TARGETS.A.sport / 2)
    },
    B: {
        focus: Math.floor(PERFECT_TARGETS.B.focus / 2),
        free: Math.floor(PERFECT_TARGETS.B.free / 2),
        sport: Math.floor(PERFECT_TARGETS.B.sport / 2)
    },
    C: {
        focus: Math.floor(PERFECT_TARGETS.C.focus / 2),
        free: Math.floor(PERFECT_TARGETS.C.free / 2),
        sport: Math.floor(PERFECT_TARGETS.C.sport / 2)
    },
    D: {
        focus: 30,
        free: 10,
        sport: 10
    }
};

const TYPE_COLORS = {
    A: "#8B6BC7",  // 柔和的紫色
    B: "#65A849",  // 柔和的绿色
    C: "#3DA6A6",  // 柔和的青色
    focus: "#4B89B7",  // 柔和的蓝色
    free: "#D47B8A",   // 柔和的粉色
    sport: "#C7B236",  // 柔和的黄色
    D: "#888888",  // 为D类型添加一个灰色
};

// 在常量定义部分添加旅程相关的样式常量
const JOURNEY_STYLES = {
    container: {
        marginBottom: "8px",  // 减小底部边距
        padding: "4px 8px",   // 减小内边距
        borderRadius: "4px",
        backgroundColor: "var(--background-secondary)", // 添加背景色以区分
    },
    title: {
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: "2px",  // 减小标题和进度条的间距
    },
    nameLink: {
        fontSize: "0.95em",   // 减小字体大小
        fontWeight: "500",    // 调整字重
        color: "var(--text-normal)",
        textDecoration: "none",
    },
    value: {
        fontSize: "0.85em",   // 减小数值字体大小
        color: "var(--text-muted)",
    },
    progressBar: {
        height: "3px",        // 减小进度条高度
        borderRadius: "1.5px",
        backgroundColor: "var(--background-modifier-border)",
        overflow: "hidden",
    },
    progressFill: {
        height: "100%",
        backgroundColor: "var(--text-accent)",
        opacity: "0.5",
        transition: "width 0.3s ease",
    },
    progressText: {
        fontSize: "0.75em",   // 减小进度文字大小
        color: "var(--text-muted)",
        marginTop: "1px",     // 减小顶部边距
        textAlign: "right",
    }
};

function generateTemplate(templateCode) {
    if (typeof templateCode !== 'string' || templateCode.length !== 4) {
        templateCode = '3311';
    }

    const [cycles, aCount, bCount, cCount] = templateCode.split('').map(Number);
    let template = [];

    // 生成循环部分
    for (let i = 0; i < cycles; i++) {
        // 添加A天
        template = template.concat(Array(aCount).fill('A'));
        // 添加B天
        template = template.concat(Array(bCount).fill('B'));
    }

    // 添加C天
    template = template.concat(Array(cCount).fill('C'));

    return template;
}

// 计算日期范围
function calculateDateRange(startDate, templateCode, skipDays = []) {
    const template = generateTemplate(templateCode);
    const skipDatesSet = new Set(skipDays.map(d => d.substring(0, 10)));

    const start = new Date(startDate);
    let currentDate = new Date(start);
    let templateIndex = 0;
    let totalDays = 0;

    // 计算结束日期
    while (templateIndex < template.length) {
        const dateStr = currentDate.toISOString().substring(0, 10);

        if (skipDatesSet.has(dateStr)) {
            // 跳过这一天，但不增加模板索引
            currentDate.setDate(currentDate.getDate() + 1);
            totalDays++;
            continue;
        }

        // 正常日期，增加模板索引
        templateIndex++;
        currentDate.setDate(currentDate.getDate() + 1);
        totalDays++;
    }

    // 确保包含了所有跳过的日期
    while (true) {
        const dateStr = currentDate.toISOString().substring(0, 10);
        if (skipDatesSet.has(dateStr)) {
            currentDate.setDate(currentDate.getDate() + 1);
            totalDays++;
        } else {
            break;
        }
    }

    const end = new Date(currentDate);
    end.setDate(end.getDate() - 1);

    return { start, end, totalDays };
}

// 创建初始日期对象
function createInitialDateObject(date, file) {
    return {
        date: new Date(date),
        file: file,
        status: "notStarted",
        type: "A",
        styles: { focus: 0, free: 0, sport: 0 }
    };
}

// 计算进度条样式
function calculateStyles(values, targetValues) {
    // 计算每个值相对于目标值的比例
    const normalizedValues = {
        focus: values.focus * targetValues.focus * 0.25,
        free: values.free * targetValues.free,
        sport: values.sport * targetValues.sport
    };

    const total = normalizedValues.focus + normalizedValues.free + normalizedValues.sport;

    if (total === 0) {
        return {
            focus: 0,
            free: 0,
            sport: 0
        };
    }

    // 归一化处理,使得三个值的和为1
    return {
        focus: normalizedValues.focus / total,
        free: normalizedValues.free / total,
        sport: normalizedValues.sport / total
    };
}

function calculateAchievementRates(values, targetValues) {
    return {
        focus: targetValues.focus === 0 ? (values.focus > 0 ? 1 : 0) :
               (values.focus >= targetValues.focus ? values.focus / targetValues.focus : 0),
        free: targetValues.free === 0 ? (values.free > 0 ? 1 : 0) :
              (values.free >= targetValues.free ? values.free / targetValues.free : 0),
        sport: targetValues.sport === 0 ? (values.sport > 0 ? 1 : 0) :
               (values.sport >= targetValues.sport ? values.sport / targetValues.sport : 0)
    };
}

function determineStatus(values, targetValues, perfectValues) {
    const rates = calculateAchievementRates(values, targetValues);
    const totalRate = rates.focus + rates.free + rates.sport;

    if (values.focus >= perfectValues.focus &&
        values.free >= perfectValues.free &&
        values.sport >= perfectValues.sport) {
        return "perfect";
    }
    if (totalRate === 0) {
        if (values.focus > 0 || values.free > 0 || values.sport > 0) {
            return "attempted";
        }
        return "failed";
    }
    if (totalRate > 0) {
        return "partial";
    }
    return "failed";
}

// 主要逻辑
const skipDays = input.skip_days || [];
const templateCode = input.template || '3311';
const { start: START_DAY, end: END_DAY } = calculateDateRange(input.start, templateCode, skipDays);

// 修改日期生成和类型分配逻辑
const template = generateTemplate(templateCode);
let templateIndex = 0;
const skipDatesSet = new Set(skipDays.map(d => d.substring(0, 10)));

// 获取日记文件并建立映射
const diaryFiles = dv.pages('"Nexus/日记"').where(file => {
    const fileDate = new Date(file.file.name.slice(0, 10));
    return fileDate >= START_DAY && fileDate <= END_DAY;
});

const dateToFileMap = new Map(
    diaryFiles.map(file => [file.file.name.slice(0, 10), file])
);

// 生成日期数组
const dates = [];
for (let date = new Date(START_DAY); date <= END_DAY; date.setDate(date.getDate() + 1)) {
    const dateStr = date.toISOString().substring(0, 10);
    const dateObj = createInitialDateObject(date, dateToFileMap.get(dateStr));

    if (skipDatesSet.has(dateStr)) {
        dateObj.type = 'D';
    } else {
        dateObj.type = template[templateIndex++];
    }

    dates.push(dateObj);
}

// 创建容器和样式
const container = dv.container;
container.style.width = "100%";
container.style.position = "relative";
container.style.backgroundColor = "var(--background-primary)";
container.style.borderRadius = "5px";
container.style.padding = "5px";

// 创建网格容器
const gridContainer = container.createDiv();
gridContainer.style.display = "grid";
gridContainer.style.gridTemplateColumns = "repeat(7, 1fr)";
gridContainer.style.gap = "8px";
gridContainer.style.marginTop = "10px";

// 根据开始日期动态生成星期标题
const weekdays = ["日", "一", "二", "三", "四", "五", "六"];
const startDayIndex = START_DAY.getDay(); // 获取开始日期是星期几
const reorderedWeekdays = [
    ...weekdays.slice(startDayIndex),
    ...weekdays.slice(0, startDayIndex)
];

// 添加重排后的星期标题
reorderedWeekdays.forEach(day => {
    const dayLabel = gridContainer.createDiv();
    dayLabel.style.textAlign = "center";
    dayLabel.style.color = "var(--text-muted)";
    dayLabel.style.fontSize = "0.9em";
    dayLabel.textContent = day;
});

// 修改判断今天的函数
function isSameDay(date1, date2) {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
}

// 渲染日期块
dates.forEach((dateObj, index) => {
    // 创建a标签作为日期块
    const dateBlock = gridContainer.createEl("a");
    dateBlock.style.aspectRatio = "1";

    // 使用新的日期比较方法
    const isToday = isSameDay(dateObj.date, new Date());

    // 根据是否是今天设置不同的圆角
    if (isToday) {
        // dateBlock.style.borderRadius = "12px";
        dateBlock.style.transform = "scale(1.05)";
        dateBlock.style.boxShadow = "0 0 8px var(--background-modifier-border)";
        dateBlock.style.animation = "perfect-pulse 2s infinite";
        dateBlock.style.border = `2px solid var(--background-modifier-border)`;
    } else {
        dateBlock.style.borderRadius = "6px";
    }

    // 其他样式保持不变
    dateBlock.style.display = "flex";
    dateBlock.style.alignItems = "center";
    dateBlock.style.justifyContent = "center";
    dateBlock.style.position = "relative";
    dateBlock.style.textDecoration = "none";
    dateBlock.style.color = "inherit";
    dateBlock.style.padding = "4px";
    dateBlock.style.boxSizing = "border-box";
    dateBlock.style.transition = "all 0.3s ease";

    // 设置日期块的基本样式
    if (dateObj.file) {
        // 存在日记文件时的设置
        dateBlock.classList.add("internal-link");
        dateBlock.dataset.href = dateObj.file.file.name;
        dateBlock.href = dateObj.file.file.name;
        dateBlock.style.transition = "all 0.3s ease";
        dateBlock.style.cursor = "pointer";

        const values = {
            focus: dateObj.file.专注 ?? 0,
            free: dateObj.file.逍遥 ?? 0,
            sport: dateObj.file.运动 ?? 0
        };

        const targetValues = input.targets?.[dateObj.type] || DEFAULT_TARGETS[dateObj.type];
        const perfectValues = PERFECT_TARGETS[dateObj.type];

        dateObj.status = determineStatus(values, targetValues, perfectValues);
        const rates = calculateAchievementRates(values, targetValues);
        dateObj.styles = calculateStyles(rates, dateObj.status === "perfect" ? perfectValues : targetValues);

        if (dateObj.status === "perfect") {
            // Perfect状态显示多彩背景
            const { focus, free, sport } = dateObj.styles;
            // dateBlock.style.color = `color-mix(in srgb, ${TYPE_COLORS[dateObj.type]}, white 25%)`;
            dateBlock.style.color = TYPE_COLORS[dateObj.type];
            dateBlock.style.position = "relative";

            dateBlock.style.backgroundImage = `
                linear-gradient(135deg,
                    ${TYPE_COLORS.focus}50 ${focus * 100}%,
                    ${TYPE_COLORS.free}50 ${focus * 100}% ${(focus + free) * 100}%,
                    ${TYPE_COLORS.sport}50 ${(focus + free) * 100}% ${(focus + free + sport) * 100}%
                ),
                linear-gradient(45deg,
                    ${TYPE_COLORS.focus} ${focus * 100}%,
                    ${TYPE_COLORS.free} ${focus * 100}% ${(focus + free) * 100}%,
                    ${TYPE_COLORS.sport} ${(focus + free) * 100}% ${sport * 100}%
                ),
                linear-gradient(-45deg,
                    ${TYPE_COLORS.focus} ${focus * 100}%,
                    ${TYPE_COLORS.free} ${focus * 100}% ${(focus + free) * 100}%,
                    ${TYPE_COLORS.sport} ${(focus + free) * 100}% ${sport * 100}%
                )
            `;
            // dateBlock.style.backgroundImage = `
            //     linear-gradient(135deg,
            //         ${TYPE_COLORS.focus}50 ${focus * 100}%,
            //         ${TYPE_COLORS.free}50 ${focus * 100}% ${(focus + free) * 100}%,
            //         ${TYPE_COLORS.sport}50 ${(focus + free) * 100}% ${(focus + free + sport) * 100}%
            //     )
            // `;
            dateBlock.style.backgroundPosition = "center, 0 0, 0 100%, 0 0, 100% 0";
            dateBlock.style.backgroundSize = "100% 100%, 98% 2px, 98% 2px";
            dateBlock.style.backgroundRepeat = "no-repeat";

            dateBlock.style.borderTop = `2px solid ${TYPE_COLORS[dateObj.type]}aa`;
            dateBlock.style.borderBottom = `2px solid ${TYPE_COLORS[dateObj.type]}aa`;
            dateBlock.style.boxShadow = `0 0 10px ${TYPE_COLORS[dateObj.type]}20`;
            dateBlock.style.backdropFilter = "brightness(1.1)";

            // 计算连击数和位置
            let combo = 1;
            let isLeftEdge = index % 7 === 0;
            let isRightEdge = index % 7 === 6;

            // 向前查找连击
            for (let i = index - 1; i >= 0; i--) {
                if (dates[i].status === "perfect") {
                    if ((i % 7 === 6 && (i + 1) % 7 === 0)) break;
                    combo++;
                } else break;
            }

            // 添加连接效果
            if (combo > 1) {
                dateBlock.style.position = "relative";

                const pseudoStyle = document.createElement('style');
                const uniqueClass = `perfect-connect-${index}`;
                dateBlock.classList.add(uniqueClass);

                let pseudoRules = '';

                // 如果不是右边缘，且右边有perfect状态，添加右连接
                if (!isRightEdge && index + 1 < dates.length && dates[index + 1].status === "perfect") {
                    pseudoRules += `
                        .${uniqueClass}::after {
                            content: '';
                            position: absolute;
                            right: -8px;
                            top: 50%;
                            width: 8px;
                            height: 90%;
                            background: linear-gradient(
                                90deg,
                                ${TYPE_COLORS[dateObj.type]}40,
                                ${TYPE_COLORS[dateObj.type]}10
                            );
                            transform: translateY(-50%);
                            z-index: 1;
                            border-radius: 0 2px 2px 0;
                            box-shadow: inset 2px 0 3px rgba(0, 0, 0, 0.1),
                                       inset -1px 0 2px rgba(255, 255, 255, 0.05);
                            backdrop-filter: blur(1px);
                        }
                    `;
                }

                // 如果不是左边缘，且左边有perfect状态，添加左连接
                if (!isLeftEdge && index > 0 && dates[index - 1].status === "perfect") {
                    pseudoRules += `
                        .${uniqueClass}::before {
                            content: '';
                            position: absolute;
                            left: -8px;
                            top: 50%;
                            width: 8px;
                            height: 90%;
                            background: linear-gradient(
                                -90deg,
                                ${TYPE_COLORS[dateObj.type]}40,
                                ${TYPE_COLORS[dateObj.type]}10
                            );
                            transform: translateY(-50%);
                            z-index: 1;
                            border-radius: 2px 0 0 2px;
                            box-shadow: inset -2px 0 3px rgba(0, 0, 0, 0.1),
                                       inset 1px 0 2px rgba(255, 255, 255, 0.05);
                            backdrop-filter: blur(1px);
                        }
                    `;
                }

                if (pseudoRules) {
                    pseudoStyle.textContent = pseudoRules;
                    document.head.appendChild(pseudoStyle);
                }

                // 调整日期块的阴影效果以配合凹陷的连接效果
                dateBlock.style.boxShadow = `
                    0 0 ${combo * 2}px ${TYPE_COLORS[dateObj.type]}40,
                    inset 0 0 4px ${TYPE_COLORS[dateObj.type]}10,
                    0 0 2px rgba(0, 0, 0, 0.1)
                `;
                dateBlock.style.zIndex = "2";
            }
        } else if (dateObj.status === "attempted") {
            // attempted状态显示单色边框，使用类型颜色
            dateBlock.style.background = "var(--background-secondary)";
            dateBlock.style.color = TYPE_COLORS[dateObj.type];
            dateBlock.style.border = `2px solid ${TYPE_COLORS[dateObj.type]}60`;
        } else if (dateObj.status === "failed") {
            // failed状态显示灰色边框
            dateBlock.style.background = "var(--background-secondary)";
            dateBlock.style.color = `${TYPE_COLORS[dateObj.type]}aa`;
            dateBlock.style.border = `2px solid var(--background-modifier-border)`;
        } else if (dateObj.status === "partial") {
            // partial状态显示多彩边框
            const { focus, free, sport } = dateObj.styles;
            dateBlock.style.background = "var(--background-secondary)";
            dateBlock.style.color = TYPE_COLORS[dateObj.type];
            dateBlock.style.position = "relative";
            dateBlock.style.borderRight = `2px solid ${TYPE_COLORS[dateObj.type]}80`;
            dateBlock.style.borderLeft = `2px solid ${TYPE_COLORS[dateObj.type]}80`;
            dateBlock.style.backgroundImage = `
                linear-gradient(45deg,
                    ${TYPE_COLORS.focus} ${focus * 100}%,
                    ${TYPE_COLORS.free} ${focus * 100}% ${(focus + free) * 100}%,
                    ${TYPE_COLORS.sport} ${(focus + free) * 100}% ${sport * 100}%
                ),
                linear-gradient(-45deg,
                    ${TYPE_COLORS.focus} ${focus * 100}%,
                    ${TYPE_COLORS.free} ${focus * 100}% ${(focus + free) * 100}%,
                    ${TYPE_COLORS.sport} ${(focus + free) * 100}% ${sport * 100}%
                )
            `;
            dateBlock.style.backgroundPosition = "0 0, 0 100%, 0 0, 100% 0";
            dateBlock.style.backgroundSize = "100% 3px, 100% 3px, 3px 100%, 3px 100%";
            dateBlock.style.backgroundRepeat = "no-repeat";
        }
    } else {
        // 不存在日记文件时的设置
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const isPast = dateObj.date.getTime() < today.getTime();

        dateBlock.style.opacity = isPast ? "0.7" : "0.5";
        dateBlock.style.cursor = "default";
        dateBlock.style.color = isPast ? "var(--background-modifier-border)" : TYPE_COLORS[dateObj.type];
        dateBlock.style.background = isPast ? "transparent" : "var(--background-secondary)";
    }

    // 显示日期
    const dateText = dateBlock.createDiv();
    dateText.textContent = dateObj.date.getDate();
    dateText.style.fontSize = "1.1em";
    dateText.style.fontWeight = "500";
    dateText.style.paddingTop = "2px";

    if (dateObj.status === "perfect") {
        dateText.style.textShadow = `
            -1px -1px 0 #333333,
            1px -1px 0 #333333,
            -1px 1px 0 #333333,
            1px 1px 0 #333333,
            0 0 4px rgba(80, 80, 80, 0.7)
        `;
    }

    // 如果是1号或者是第一个日期，显示月
    if (dateObj.date.getDate() === 1 || index === 0) {
        const monthLabel = dateBlock.createDiv();
        monthLabel.style.position = "absolute";
        monthLabel.style.bottom = "0";
        monthLabel.style.right = "0";
        monthLabel.style.fontSize = "0.6em";
        monthLabel.style.color = "var(--text-muted)";
        monthLabel.textContent = `${dateObj.date.getMonth() + 1}月`;
    }
});

// 获取今天的日期和类型
const today = new Date();
const todayIndex = dates.findIndex(dateObj => isSameDay(dateObj.date, today));

// 如果今天在阶段范围内，显示今日需求
if (todayIndex !== -1) {
    const todayType = dates[todayIndex].type;
    const todayTargets = input.targets?.[todayType] || DEFAULT_TARGETS[todayType];

    const requirementsContainer = container.createDiv();
    requirementsContainer.style.marginTop = "8px";
    requirementsContainer.style.marginBottom = "12px";
    requirementsContainer.style.padding = "8px 12px";
    requirementsContainer.style.fontSize = "0.9em";
    requirementsContainer.style.color = "var(--text-muted)";
    requirementsContainer.style.borderLeft = `3px solid ${TYPE_COLORS[todayType]}40`;
    requirementsContainer.style.backgroundColor = "var(--background-secondary)";
    requirementsContainer.style.borderRadius = "4px";

    const todayInfo = requirementsContainer.createDiv();
    todayInfo.style.marginBottom = "4px";
    todayInfo.innerHTML = `今日为 <span style="color: ${TYPE_COLORS[todayType]}; font-weight: 500;">${todayType}</span> 日，目标：`;

    const targetsList = requirementsContainer.createDiv();
    targetsList.style.display = "flex";
    targetsList.style.gap = "12px";
    targetsList.style.fontSize = "0.85em";

    const targets = [
        { name: "专注", key: "focus", color: TYPE_COLORS.focus },
        { name: "逍遥", key: "free", color: TYPE_COLORS.free },
        { name: "运动", key: "sport", color: TYPE_COLORS.sport }
    ];

    targets.forEach(target => {
        const targetSpan = targetsList.createSpan();
        targetSpan.innerHTML = `${target.name}: <span style="color: ${target.color}; font-weight: 500;">${todayTargets[target.key]}</span> 分钟`;
    });
}


// 添加时间计算相关的工具函数
function calculateTimeDifference(timeRange) {
    if (!timeRange) return 0;

    // 标准化时间格式（处理单位数的小时）
    const normalizedTimeRange = timeRange.replace(/(\d{1}):(\d{2})/g, '$1:$2');
    const times = normalizedTimeRange.split('-');

    if (times.length !== 2) return 0;

    const [start, end] = times;
    const timeToMinutes = timeStr => {
        const [hours, minutes] = timeStr.trim().split(':').map(Number);
        return hours * 60 + minutes;
    };
    return timeToMinutes(end) - timeToMinutes(start);
}

// 计算灵石
function calculateSoulstonesFromTime(totalMinutes, flowType) {
    const FLOW_TYPES = {
        STUDY_PRACTICE: ["学习", "实践"],
        EXERCISE_EXPLORE: ["练习", "探索"]
    };

    if (FLOW_TYPES.STUDY_PRACTICE.includes(flowType)) {
        return calculateStudyPracticeSoulstones(totalMinutes);
    } else if (FLOW_TYPES.EXERCISE_EXPLORE.includes(flowType)) {
        return calculateExerciseExploreSoulstones(totalMinutes);
    }
    return 0;
}

function calculateStudyPracticeSoulstones(minutes) {
    const TWO_HOURS = 2 * 60;
    const SIX_HOURS = 6 * 60;
    const TEN_HOURS = 10 * 60;

    if (minutes >= TEN_HOURS) return 3;
    if (minutes >= SIX_HOURS) return 2;
    if (minutes >= TWO_HOURS) return 1;
    return 0;
}

function calculateExerciseExploreSoulstones(minutes) {
    const HALF_HOUR = 30;
    const ONE_HOUR = 60;
    const HOUR_INCREMENT = 60;

    if (minutes < HALF_HOUR) return 0;
    if (minutes < ONE_HOUR) return 0.2;

    const additionalHours = Math.floor((minutes - ONE_HOUR) / HOUR_INCREMENT);
    const soulstones = 0.5 + (additionalHours * 0.3);

    return Math.min(soulstones, 3);
}

// 计算印记灵石
async function calculateAllMarksSoulstones(diaryFiles) {
    // 用于存储每个印记的灵石统计
    const markSoulstones = new Map();

    for (const diaryFile of diaryFiles) {
        const tasks = diaryFile.file.tasks;
        if (!tasks) continue;

        console.log(`处理日记: ${diaryFile.file.name}`);
        const dailyMarkMinutes = new Map();

        // 统计每个印记在当天的总时间
        for (const task of tasks) {
            const timeMatch = task.text.match(/(\d{1,2}[:：]\d{2}-\d{1,2}[:：]\d{2})/);
            if (!timeMatch) continue;

            const links = task.outlinks;
            if (!links) continue;

            const timeRange = timeMatch[1].replace(/：/g, ':');
            const minutes = calculateTimeDifference(timeRange);

            for (const link of links) {
                const markFile = dv.page(link.path);
                if (!markFile || !markFile.流转) continue;

                const markId = link.path;
                if (!dailyMarkMinutes.has(markId)) {
                    dailyMarkMinutes.set(markId, {
                        minutes: 0,
                        flowType: markFile.流转
                    });
                }
                dailyMarkMinutes.get(markId).minutes += minutes;
            }
        }

        // 计算每个印记当天的灵石
        for (const [markId, data] of dailyMarkMinutes) {
            const soulstones = calculateSoulstonesFromTime(data.minutes, data.flowType);
            console.log(`  印记 ${markId.split('/').pop()}: ${data.minutes}分钟 -> ${soulstones}灵石 (${data.flowType})`);

            if (!markSoulstones.has(markId)) {
                markSoulstones.set(markId, 0);
            }
            markSoulstones.set(markId, markSoulstones.get(markId) + soulstones);
        }
    }

    return markSoulstones;
}

async function calculateJourneyProgress(markSoulstones, journeyName) {
    let totalSoulstones = 0;

    for (const [markId, soulstones] of markSoulstones) {
        const markFile = dv.page(markId);
        if (!markFile || !markFile.旅程) continue;

        // 检查日期是否为D日
        const dateStr = markFile.file?.name?.substring(0, 10);
        if (dateStr && skipDatesSet.has(dateStr)) continue;

        if (markFile.旅程.path.includes(journeyName)) {
            totalSoulstones += soulstones;
        }
    }

    return totalSoulstones;
}

// 修改进度条渲染部分
if (input.journeys && input.journeys.length > 0) {
    const progressContainer = container.createDiv();
    progressContainer.style.marginTop = "16px";  // 减小顶部边距
    progressContainer.style.display = "grid";     // 使用网格布局
    progressContainer.style.gap = "4px";          // 设置网格间距
    progressContainer.style.gridTemplateColumns = "repeat(auto-fit, minmax(250px, 1fr))"; // 响应式布局

    // 先计算所有印记的灵石
    const markSoulstones = await calculateAllMarksSoulstones(diaryFiles);

    for (const journey of input.journeys) {
        const { name, expect } = journey;
        const currentValue = await calculateJourneyProgress(markSoulstones, name);
        const percentage = Math.min((currentValue / expect) * 100, 100);

        // 创建旅程容器
        const journeyContainer = progressContainer.createDiv();
        Object.assign(journeyContainer.style, JOURNEY_STYLES.container);

        // 标题和数值容器
        const titleDiv = journeyContainer.createDiv();
        Object.assign(titleDiv.style, JOURNEY_STYLES.title);

        // 旅程名称链接
        const nameLink = titleDiv.createEl("a");
        nameLink.textContent = name;
        nameLink.classList.add("internal-link");
        nameLink.dataset.href = name;
        nameLink.href = name;
        Object.assign(nameLink.style, JOURNEY_STYLES.nameLink);

        // 数值显示
        const valueSpan = titleDiv.createSpan();
        valueSpan.textContent = `${currentValue.toFixed(1)} / ${expect}`;
        Object.assign(valueSpan.style, JOURNEY_STYLES.value);

        // 进度条容器
        const progressBar = journeyContainer.createDiv();
        Object.assign(progressBar.style, JOURNEY_STYLES.progressBar);

        // 进度条填充
        const progressFill = progressBar.createDiv();
        Object.assign(progressFill.style, JOURNEY_STYLES.progressFill);
        progressFill.style.width = `${percentage}%`;

        // 进度百分比
        const progressText = journeyContainer.createDiv();
        progressText.textContent = `${percentage.toFixed(1)}%`;
        Object.assign(progressText.style, JOURNEY_STYLES.progressText);
    }
}
