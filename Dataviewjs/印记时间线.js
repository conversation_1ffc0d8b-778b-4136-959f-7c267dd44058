/*
 * @作者: roam1n
 * @版本: 0.0.2
 * @最后更新: 2025-03-24
 */

// 输入
const CONTAINER = input.container;
const TITLE = input.title || "";
const NORMAL = input.normal;
const IMPORTANT = input.important || [];
const URGENT = input.urgent || [];
const ISLEFT = input.direction === 'right' ? false: true;
const ISASC = input.isasc || false;

if (!CONTAINER) return dv.paragraph("缺少container参数");
if (!NORMAL) return dv.paragraph("缺少normal参数");
if (!NORMAL.length && !IMPORTANT.length && !URGENT.length) return;

const TODAY = new Date();
TODAY.setHours(0, 0, 0, 0);
let lastYear = TODAY.getFullYear();
let lastDate = null;

const timelineSection = document.createElement("div");
CONTAINER.appendChild(timelineSection);
timelineSection.style.flex = "1";
timelineSection.style.display = "flex";
timelineSection.style.flexDirection = "column";
timelineSection.style.gap = "8px";
timelineSection.style.backgroundColor = "var(--background-secondary)";
timelineSection.style.borderRadius = "4px";
timelineSection.style.padding = "10px";
timelineSection.style.position = "relative";
timelineSection.style.maxHeight = "60vh";
timelineSection.style.overflowY = "auto";
timelineSection.style.fontSize = "14px";

if (TITLE) {
    // 时间线标题
    const timelineTitle = document.createElement("div");
    timelineSection.appendChild(timelineTitle);
    timelineTitle.textContent = TITLE;
    timelineTitle.style.fontWeight = "bold";
    timelineTitle.style.color = "var(--text-normal)";
    timelineTitle.style.opacity = "0.9";
    if (ISLEFT) {
        timelineTitle.style.textAlign = "left";
        timelineTitle.style.marginLeft = "42px";
    } else {
        timelineTitle.style.textAlign = "right";
        timelineTitle.style.marginRight = "42px";
    }
}

function createTimeLineItem (item, parent) {
    // 显示首次出现的年份（最新的年份如果是今年不显示）
    if (item.year && item.year != lastYear) { // 可能是字符串对比数字
        const yearMarker = document.createElement("div");
        yearMarker.className = "year-marker";
        yearMarker.textContent = item.year;
        yearMarker.style.color = "var(--text-accent)";
        yearMarker.style.textAlign = ISLEFT ? "left" : "right";
        yearMarker.style.paddingLeft = ISLEFT ? "48px" : "0";
        yearMarker.style.paddingRight = ISLEFT ? "0" : "48px";
        parent.appendChild(yearMarker);
        lastYear = item.year;
        lastDate = null;
    }

    const timeLineItem = document.createElement("div");
    parent.appendChild(timeLineItem);
    timeLineItem.className = "timeline-item";
    timeLineItem.style.display = "flex";
    timeLineItem.style.flexDirection = ISLEFT? "row" : "row-reverse";
    timeLineItem.style.backgroundColor = `var(--background-primary)`;
    timeLineItem.style.cursor = "pointer";
    timeLineItem.style.padding = "6px 8px";
    timeLineItem.style.backgroundColor = "var(--background-primary)";
    timeLineItem.style.borderRadius = "4px";
    timeLineItem.style.margin = "2px 0";
    timeLineItem.style.transition = "all 0.2s ease";

    // 时间
    const timeLineTime = document.createElement("div");
    timeLineItem.appendChild(timeLineTime);
    timeLineTime.style.display = "flex";
    timeLineTime.style.flexDirection = "column";
    timeLineTime.style.gap = "2px";
    timeLineTime.style.position = "relative";
    timeLineTime.style.alignItems = ISLEFT? "flex-end" : "flex-start";
    timeLineTime.style.justifyContent = "center";
    timeLineTime.style.width = "42px";
    timeLineTime.style.boxSizing = "border-box";

    const timeDate = document.createElement("div");
    timeLineTime.appendChild(timeDate);
    timeDate.className = "time-date";
    timeDate.textContent = item.date;
    timeDate.style.fontSize = "0.8em";
    timeDate.style.lineHeight = "1";
    timeDate.style.color = `var(--color-task-${item.taskType})`;
    timeDate.style.transition = "all 0.2s ease";

    if (item.date && item.date === lastDate) {
        timeDate.style.opacity = "0";
    } else {
        lastDate = item.date;
    }

    // 专注信息显示
    if (item.专注) {
        const focusInfo = document.createElement("div");
        timeLineTime.appendChild(focusInfo);
        focusInfo.className = "focus-info";
        focusInfo.textContent = item.专注;
        focusInfo.style.fontSize = "0.7em";
        focusInfo.style.lineHeight = "1";
        focusInfo.style.color = "var(--text-muted)";
        focusInfo.style.opacity = "0.7";
        focusInfo.style.transition = "all 0.2s ease";

    }

    // 状态图标
    const timeLineIconBox = document.createElement("div");
    timeLineItem.appendChild(timeLineIconBox);
    timeLineIconBox.style.display = "flex";
    timeLineIconBox.style.justifyContent = "center";
    timeLineIconBox.style.alignItems = "center";
    timeLineIconBox.style.width = "30px";

    const timeLineIcon = document.createElement("div");
    timeLineIconBox.appendChild(timeLineIcon);
    timeLineIcon.setAttribute("data-icon", item.taskType);
    timeLineIcon.style.display = "flex";
    timeLineIcon.style.justifyContent = "center";
    timeLineIcon.style.alignItems = "center";
    timeLineIcon.style.width = "16px";
    timeLineIcon.style.height = "16px";
    timeLineIcon.style.backgroundColor = `var(--color-task-${item.taskType})`;

    // 时间线内容及文件链接
    const contentSection = document.createElement("div");
    timeLineItem.appendChild(contentSection);
    contentSection.style.display = "flex";
    contentSection.style.flexDirection = "column";
    contentSection.style.justifyContent = "center";
    contentSection.style.gap = "4px";
    contentSection.style.flex = "1";

    if (item.text) {
        const contentText = document.createElement("div");
        contentSection.appendChild(contentText);
        contentText.textContent = item.text;
        contentText.style.fontSize = "0.9em";
        contentText.style.textAlign = ISLEFT? "left" : "right";
    }

    if (item.stepName && item.stepType) {
        const simpleName = item.stepName.replace(/^(?:[0-9]{4}|[0-9]{6})-(.+)$/, '$1');
        const fileLink = document.createElement("a");
        contentSection.appendChild(fileLink);
        fileLink.textContent = simpleName;
        fileLink.href = item.path;
        fileLink.className = "internal-link";
        fileLink.dataset['href'] = item.path;
        fileLink.style.lineHeight = "1";
        fileLink.style.textAlign = ISLEFT? "left" : "right";
        fileLink.style.color = `var(--color-task-${item.stepType})`;
        fileLink.style.fontSize = item.text ? "0.7em" : "0.8em";
    }
}

function createTimeLineList (list, parent, listType) {
    const timeLineList = document.createElement("div");
    parent.appendChild(timeLineList);
    timeLineList.style.display = "flex";
    timeLineList.style.flexDirection = "column";
    timeLineList.style.gap = "2px";
    timeLineList.style.position = "relative";

    if (['urgent', 'important'].includes(listType)) {
        timeLineList.style.marginBottom = "12px";
        timeLineList.style.position = "relative";
        const line = document.createElement("div");
        line.style.position = "absolute";
        line.style.bottom = "-10px";
        line.style.height = "2px";
        line.style.left = "0";
        line.style.right = "0";
        line.style.backgroundColor = listType === 'urgent'? 'hsla(35, 75%, 48%, 0.15)' : 'var(--background-modifier-border)' ;
        timeLineList.appendChild(line);
    }

    const style = document.createElement('style');
    style.textContent = `
        .timeline-item:hover {
            background-color: var(--background-secondary-alt);
            transform: translateX(2px);
        }
        .timeline-item:hover .time-date {
            opacity: 1 !important;
        }
        .timeline-item:hover .focus-info {
            opacity: 0.7 !important;
        }`
    parent.appendChild(style);
    timeLineList.className = "timeline-list";

    list.sort((a, b) => {
        // 没有日期的项目放在最后
        if (!a.year && !a.date) return ISASC ? 1 : -1;
        if (!b.year && !b.date) return ISASC ? -1 : 1;
        
        // 按年份排序
        if (a.year !== b.year) {
            return ISASC ? a.year - b.year : b.year - a.year;
        }
        
        // 按日期排序
        return ISASC ? a.date - b.date : b.date - a.date;
    });

    // 重置一下年份
    lastYear = TODAY.getFullYear();
    list.forEach((item) => {
        createTimeLineItem(item, timeLineList);
    });
}

// 带入数据
if (URGENT.length > 0) {
    createTimeLineList(URGENT, timelineSection, "urgent");
}
if (IMPORTANT.length > 0) {
    createTimeLineList(IMPORTANT, timelineSection, "important");
}
createTimeLineList(NORMAL, timelineSection, "normal");
