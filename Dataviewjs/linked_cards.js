/*
 * @描述: 以卡片形式展示引用当前文件的文件
 * @作者: Claude
 * @版本: 0.0.1
 */

// 创建主容器
const container = dv.container;
container.style.width = "100%";
container.style.display = "grid";
container.style.gridTemplateColumns = "repeat(auto-fill, minmax(280px, 1fr))";
container.style.gap = "12px";
container.style.padding = "8px";

// 获取当前文件
const folders = input?.folders || ["Nexus"];
const currentFile = dv.current().file;

// 获取所有引用当前文件的页面
const linkedPages = folders.reduce((acc, folder) => {
    const pages = dv.pages(`"${folder}"`).sort(p=>p.file.ctime, 'desc');
    return [...acc, ...pages];
}, [])
    .filter(page => page.from && (
        Array.isArray(page.from)
            ? page.from.find(l => l.path === currentFile.path)
            : page.from.path === currentFile.path
    ))
    .sort(p => p.file.folder);

// 为每个页面创建卡片
linkedPages.forEach(page => {
    // 创建卡片容器
    const card = container.createEl("a");
    card.classList.add("internal-link");
    card.dataset.href = page.file.path;
    card.href = page.file.path;
    Object.assign(card.style, {
        backgroundColor: "var(--background-primary)",
        borderRadius: "8px",
        padding: "12px",
        display: "flex",
        flexDirection: "column",
        boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
        transition: "transform 0.2s ease, box-shadow 0.2s ease",
        minHeight: "40px",
        maxWidth: "100%",
        position: "relative",
        overflow: "hidden",
        textDecoration: "none",
    });

    // 添加悬停效果
    card.addEventListener("mouseenter", () => {
        card.style.transform = "translateY(-2px)";
        card.style.boxShadow = "0 4px 8px rgba(0, 0, 0, 0.15)";
    });
    card.addEventListener("mouseleave", () => {
        card.style.transform = "translateY(0)";
        card.style.boxShadow = "0 2px 4px rgba(0, 0, 0, 0.1)";
    });

    // 创建标题链接
    const titleLink = card.createDiv();
    titleLink.textContent = page.file.name;
    Object.assign(titleLink.style, {
        fontSize: "0.95em",
        fontWeight: "500",
        marginBottom: "8px",
        color: "var(--text-normal)",
        display: "block",
        overflow: "hidden",
        textOverflow: "ellipsis",
        whiteSpace: "nowrap"
    });

    // 创建底部信息容器
    const footer = card.createDiv();
    Object.assign(footer.style, {
        marginTop: "auto",
        paddingTop: "8px",
        borderTop: "1px solid var(--background-modifier-border)",
        display: "flex",
        flexWrap: "wrap",
        gap: "4px",
        alignItems: "flex-start",
        fontSize: "0.75em",
        color: "var(--text-muted)",
        lineHeight: "1.4"
    });

    // 添加文件夹信息
    const folderInfo = footer.createDiv();
    folderInfo.textContent = `📁 ${page.file.folder}`;
    Object.assign(folderInfo.style, {
        marginRight: "6px",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
        maxWidth: "100%"
    });

    // 添加frontmatter信息
    const frontmatterInfo = footer.createDiv();
    Object.assign(frontmatterInfo.style, {
        display: "flex",
        flexWrap: "wrap",
        gap: "4px",
        flex: "1",
        maxWidth: "100%"
    });

    // 过滤和格式化frontmatter
    const relevantFrontmatter = Object.entries(page)
        .filter(([key]) => !["file", "from", "position"].includes(key))
        .map(([key, value]) => {
            const tag = frontmatterInfo.createDiv();
            Object.assign(tag.style, {
                padding: "1px 4px",
                // backgroundColor: "var(--background-secondary)",
                borderRadius: "3px",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
                maxWidth: "150px"
            });
            tag.textContent = `${key}:${value}`;
            return tag;
        });

    if (relevantFrontmatter.length === 0) {
        frontmatterInfo.textContent =  "";
    }
});

// 如果没有链接的页面，显示提示信息
if (linkedPages.length === 0) {
    const message = container.createDiv();
    Object.assign(message.style, {
        textAlign: "center",
        color: "var(--text-muted)",
        padding: "20px",
        gridColumn: "1 / -1"
    });
    message.textContent = "没有找到引用当前文件的页面";
}
