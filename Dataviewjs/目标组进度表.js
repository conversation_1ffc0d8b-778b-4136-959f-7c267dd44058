/*
 * @描述: 展示目标进度表
 * @作者: Claude
 * @版本: 0.0.5
 */

// 创建主容器
const container = dv.container;
container.style.width = "100%";
container.style.display = "flex";
container.style.flexWrap = "wrap";
container.style.gap = "8px";
container.style.padding = "4px";
container.style.position = "relative"; // 添加相对定位

// 创建提示框元素
document.querySelectorAll('.progress-tooltip').forEach(el => el.remove()); // 清除已存在的 progress-tooltip 元素
const tooltip = document.body.createEl("div", { attr: { class: "progress-tooltip" } });
Object.assign(tooltip.style, {
    position: "fixed",
    backgroundColor: "var(--background-primary)",
    color: "var(--text-normal)",
    padding: "4px 8px",
    borderRadius: "4px",
    fontSize: "12px",
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.2)",
    border: "1px solid var(--background-modifier-border)",
    zIndex: "999999",
    display: "none",
    pointerEvents: "none",
    maxWidth: "200px",
    wordWrap: "break-word",
    transform: "translate(-50%, -100%)",
    marginTop: "-8px",
});

// 获取目标数据
const targets = input?.targets || {};

// 计算最大的指标数量，用于确定进度条宽度
const maxIndicators = Math.max(...Object.values(targets).map(arr => arr.length));
const progressWidth = 24; // 每个进度条的固定宽度（像素）

// 进度条颜色数组
const progressColorSchemes = [
    // 青色系
    ["#80DEEA", "#26C6DA", "#00ACC1"],
    // 蓝色系
    ["#90CAF9", "#42A5F5", "#1E88E5"],
    // 紫色系
    ["#CE93D8", "#AB47BC", "#8E24AA"],
    // 红色系
    ["#EF9A9A", "#EF5350", "#E53935"],
    // 橙色系
    ["#FFCC80", "#FFA726", "#FB8C00"],
    // 绿色系
    ["#A5D6A7", "#66BB6A", "#43A047"]
];

// 获取特定卡片和指标的颜色
const getProgressColor = (cardIndex, indicatorIndex) => {
    const schemeIndex = cardIndex % progressColorSchemes.length;
    const colorIndex = indicatorIndex % 3;
    return progressColorSchemes[schemeIndex][colorIndex];
};

// 为每个方向创建卡片
Object.entries(targets).forEach(([direction, indicators], cardIndex) => {
    // 创建卡片容器
    const card = container.createEl("div");
    Object.assign(card.style, {
        backgroundColor: "var(--background-primary)",
        borderRadius: "4px",
        border: "1px solid var(--background-modifier-border)",
        padding: "8px",
        display: "flex",
        flexDirection: "column",
        boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
        minHeight: "120px",
    });

    // 创建标题
    const title = card.createEl("div");
    title.textContent = direction;
    Object.assign(title.style, {
        fontSize: "0.9em",
        fontWeight: "500",
        marginBottom: "4px",
        textAlign: "center",
        letterSpacing: "0.1em",
        color: "var(--text-normal)",
    });

    // 创建进度条容器
    const progressContainer = card.createEl("div");
    Object.assign(progressContainer.style, {
        display: "flex",
        gap: "4px",
        height: "100%",
        alignItems: "flex-end",
        width: `${indicators.length * (progressWidth + 4)}px`, // 4px是gap的宽度
    });

    // 为每个指标创建进度条
    indicators.forEach((indicator, index) => {
        const number = indicator.number || 0;
        const color = getProgressColor(cardIndex, index);

        // 创建进度条包装器
        const progressWrapper = progressContainer.createEl("div");
        Object.assign(progressWrapper.style, {
            width: `${progressWidth}px`,
            height: "100%",
            display: "flex",
            flexDirection: "column",
            justifyContent: "flex-end",
            backgroundColor: "var(--background-secondary)",
            borderRadius: "4px",
            position: "relative",
        });

        // 添加提示信息显示逻辑
        progressWrapper.addEventListener("mousemove", (e) => {
            tooltip.style.display = "block";
            tooltip.textContent = `${indicator.text} (${Math.round(number * 100)}%)`;

            // 更新提示框位置
            tooltip.style.left = `${e.clientX}px`;
            tooltip.style.top = `${e.clientY}px`;
        });
        progressWrapper.addEventListener("mouseleave", () => {
            tooltip.style.display = "none";
        });


        // 创建进度条
        const progress = progressWrapper.createEl("div");
        Object.assign(progress.style, {
            width: "100%",
            backgroundColor: color,
            borderRadius: "4px",
            transition: "height 0.3s ease",
            height: `${Math.max(0, number * 100)}%`,
            minHeight: "0px",
            cursor: "pointer",
        });

        // 添加悬停效果
        progress.addEventListener("mouseenter", () => {
            progress.style.opacity = "0.7";
        });
        progress.addEventListener("mouseleave", () => {
            progress.style.opacity = "1";
        });
    });
});

// 如果没有目标数据，显示提示信息
if (Object.keys(targets).length === 0) {
    const message = container.createEl("div");
    Object.assign(message.style, {
        textAlign: "center",
        color: "var(--text-muted)",
        padding: "20px",
        width: "100%",
    });
    message.textContent = "没有找到目标数据";
}
