/*
 * @描述: 日历展示
 * @作者: roam1n
 * @版本: 0.0.2
 * @最后更新: 2025-03-02
 */

// 常量定义
const PERFECT_TARGETS = {
    A: { focus: 520, sport: 60, free: 20 },
    B: { focus: 410, sport: 90, free: 20 },
    C: { focus: 410, sport: 90, free: 20 },
    D: { focus: 0,   sport: 30, free: 300 }
};

const TYPE_COLORS = {
    A: "#8B6BC7",  // 柔和的紫色
    B: "#65A849",  // 柔和的绿色
    C: "#3DA6A6",  // 柔和的青色
    D: "#D47B8A", // 柔和的粉色
    focus: "#4B89B7",  // 柔和的蓝色
    free: "#D47B8A",   // 柔和的粉色
    sport: "#C7B236",  // 柔和的黄色
}

// 获取最近21+6天的日记数据
const diaryFiles = dv.pages('"Nexus/日记"').sort(p => p.file.name, 'desc').limit(27);

// 获取最早和最晚的日期
const dates = diaryFiles.limit(21).map(file => new Date(file.file.name.slice(0, 10)));
const minDate = new Date(Math.min(...dates));
const maxDate = new Date(Math.max(...dates));

// 计算开始和结束日期，确保从周日到周六
const startDate = minDate;
while (startDate.getDay() !== 0) { // 找到最近的周日
    startDate.setDate(startDate.getDate() - 1);
}
const endDate = maxDate;
while (endDate.getDay() !== 6) { // 找到最近的周六
    endDate.setDate(endDate.getDate() + 1);
}

// 计算日期样式所需要的点
function calculateDiaryPionts(diaryEntry, type) {
    if (!diaryEntry) {
        return [0, 0, 0, 0, 0];
    }
    let focus = diaryEntry.专注 ?? 0;
    let free = diaryEntry.逍遥 ?? 0;
    let sport = diaryEntry.运动 ?? 0;
    const { focus: targetFocus, free: targetFree, sport: targetSport } = PERFECT_TARGETS[type];
    focus = focus / targetFocus / 3;
    sport = sport / targetSport / 3;
    free = free / targetFree / 3;
    const total = focus + free + sport;
    if (total > 1) {
        return [33.3, 66.7, 100, 100, 100];
    }
    const point1 = focus * 100;
    const point2 = point1 + sport * 100;
    const point3 = point2 + free * 100;
    const point4 = point3 + Math.max(33.3 - point1, 0);
    const point5 = Math.min(point3 + Math.max(66.7 - point2, 0), 100);
    return [point1, point2, point3, point4, point5];
}

// 计算日期的状态
function calculateDateStatus(date, isExist, point) {
    if (!isExist) {
        // 过去 未来
        return date > new Date() ? "future" : "past";
    }

    if (point === 100) {
        return "perfect";
    } else if (point === 0) {
        return "inactive";
    }

    return "normal";
}

// 创建日期数组，补充未产生活动的日期
const datesArray = [];
for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
    const dateStr = date.toISOString().substring(0, 10);
    const diaryEntry = diaryFiles.find(file => file.file.name.startsWith(dateStr));
    const type = diaryEntry?.日期类型 || "A";
    const points = calculateDiaryPionts(diaryEntry, type);
    datesArray.push({
        date: new Date(dateStr),
        name: dateStr,
        points: points,
        type: type,
        file: diaryEntry || null, // 如果没有日记文件，则为null
        status: calculateDateStatus(date, diaryEntry, points[points.length - 1]), // 记录状态
    });
}

if (!!app?.calcNextDailyType) {
    const nextTypes = calcNextDailyType(datesArray.map(date => date.type), datesArray.filter(date => date.status === "future").length);
    for (let i = 0; i < nextTypes.length; i++) {
        datesArray[datesArray.length - 1 - i].type = nextTypes[nextTypes.length - 1 - i];
    }
}

// 创建容器和样式
const container = dv.container;
container.style.width = "100%";
container.style.position = "relative";
container.style.backgroundColor = "var(--background-primary)";
container.style.borderRadius = "5px";
container.style.padding = "5px";
container.style.fontSize = `${Math.min(0.035 * container.clientWidth, 17)}px`;

// 创建网格容器
const gridContainer = container.createDiv();
gridContainer.style.display = "grid";
gridContainer.style.gridTemplateColumns = "repeat(7, 1fr)";
gridContainer.style.gap = "0.6em";
gridContainer.style.columnGap = "0.4em";
gridContainer.style.marginTop = "0.8em";

// 根据开始日期动态生成星期标题
const weekdays = ["日", "一", "二", "三", "四", "五", "六"];
const startDayIndex = startDate.getDay(); // 获取开始日期是星期几
const reorderedWeekdays = [
    ...weekdays.slice(startDayIndex),
    ...weekdays.slice(0, startDayIndex)
];

// 添加重排后的星期标题
reorderedWeekdays.forEach(day => {
    const dayLabel = gridContainer.createDiv();
    dayLabel.style.textAlign = "center";
    dayLabel.style.color = "var(--text-muted)";
    dayLabel.style.fontSize = "0.9em";
    dayLabel.textContent = day;
});

// 渲染日期块
datesArray.forEach(dateObj => {
    // 创建a标签作为日期块
    const dateBlock = gridContainer.createEl("a");
    dateBlock.style.aspectRatio = "1/0.95";
    dateBlock.style.display = "flex";
    dateBlock.style.alignItems = "center";
    dateBlock.style.justifyContent = "center";
    dateBlock.style.position = "relative";
    dateBlock.style.textDecoration = "none";
    dateBlock.style.color = "inherit";
    dateBlock.style.padding = "4px";
    dateBlock.style.boxSizing = "border-box";
    dateBlock.style.transition = "all 0.3s ease";

    // 判断今天
    const isToday = dateObj.date.toISOString().slice(0, 10) === new Date().toISOString().slice(0, 10);

    if (isToday) {
        dateBlock.style.transform = "scale(1.05)";
        dateBlock.style.boxShadow = "0 0 8px var(--background-modifier-border)";
        dateBlock.style.animation = "perfect-pulse 2s infinite";
    } else {
        dateBlock.style.borderRadius = "0.2em 0.2em 0.4em 0.4em";
    }

    // 根据记录状态设置样式
    if (dateObj.status === "past") {
        dateBlock.style.opacity = "0.7";
        dateBlock.style.cursor = "default";
        dateBlock.style.color = "var(--background-modifier-border)";
        dateBlock.style.background = "transparent";
        } else if (dateObj.status === "future") {
        dateBlock.style.opacity = "0.5";
        dateBlock.style.cursor = "default";
        dateBlock.style.color = TYPE_COLORS[dateObj.type];
        dateBlock.style.background = "var(--background-secondary)";
    } else {
        dateBlock.classList.add("internal-link");
        dateBlock.dataset.href = dateObj.file.file.name;
        dateBlock.href = dateObj.file.file.name;
        dateBlock.style.transition = "all 0.3s ease";
        dateBlock.style.cursor = "pointer";

        const [point1, point2, point3, point4, point5] = dateObj.points;

        if (dateObj.status === "perfect") {
            dateBlock.style.color = TYPE_COLORS[dateObj.type];
            dateBlock.style.position = "relative";

            dateBlock.style.backgroundImage = `
                linear-gradient(135deg,
                    ${TYPE_COLORS.focus}50 ${point1}%,
                    ${TYPE_COLORS.sport}50 ${point1}% ${point2}%,
                    ${TYPE_COLORS.free}50 ${point2}% ${point3}%
                ),
                linear-gradient(45deg,
                    ${TYPE_COLORS.focus} ${point1}%,
                    ${TYPE_COLORS.sport} ${point1}% ${point2}%,
                    ${TYPE_COLORS.free} ${point2}% ${point3}%
                )
            `;
            dateBlock.style.backgroundPosition = "center, 0 0, 0 100%, 0 0, 100% 0";
            dateBlock.style.backgroundSize = "100% 100%, 100% 5px, 100% 2px";
            dateBlock.style.backgroundRepeat = "no-repeat";

            dateBlock.style.borderTop = `1px solid ${TYPE_COLORS[dateObj.type]}aa`;
            dateBlock.style.borderBottom = `2px solid ${TYPE_COLORS[dateObj.type]}aa`;
            dateBlock.style.boxShadow = `0 0 10px ${TYPE_COLORS[dateObj.type]}20`;
            dateBlock.style.backdropFilter = "brightness(1.1)";

        } else if (dateObj.status === "inactive") {
            // failed状态显示灰色边框
            dateBlock.style.background = "var(--background-secondary)";
            dateBlock.style.color = `${TYPE_COLORS[dateObj.type]}aa`;
            dateBlock.style.border = `2px solid var(--background-modifier-border)`;

        } else if (dateObj.status === "normal") {
            // partial状态显示多彩边框
            dateBlock.style.background = "var(--background-secondary)";
            dateBlock.style.color = TYPE_COLORS[dateObj.type];
            dateBlock.style.position = "relative";
            dateBlock.style.borderBottom = `2px solid ${TYPE_COLORS[dateObj.type]}50`;
            dateBlock.style.backgroundImage = `
                linear-gradient(45deg,
                    ${TYPE_COLORS.focus} 0% ${point1}%,
                    ${TYPE_COLORS.sport} ${point1}% ${point2}%,
                    ${TYPE_COLORS.free} ${point2}% ${point3}%,
                    ${TYPE_COLORS[dateObj.type]}40 ${point3}% ${point4}%,
                    var(--background-secondary) ${point4}% ${point5}%,
                    var(--background-primary) ${point5}% 100%
                )
            `;
            dateBlock.style.backgroundPosition = "0 0, 0 100%, 0 0, 100% 0";
            dateBlock.style.backgroundSize = "100% 5px, 100% 5px, 5px 100%, 5px 100%";
            dateBlock.style.backgroundRepeat = "no-repeat";
        }
    }

    // 显示日期
    const dateText = dateBlock.createDiv();
    dateText.textContent = dateObj.date.getDate();
    dateText.style.fontSize = "1.1em";
    dateText.style.fontWeight = "500";
    dateText.style.paddingTop = "4px";

    if (dateObj.status === "perfect") {
        dateText.style.textShadow = `
            -1px -1px 0 #333333,
            1px -1px 0 #333333,
            -1px 1px 0 #333333,
            1px 1px 0 #333333,
            0 0 4px rgba(80, 80, 80, 0.7)
        `;
    }

    // 如果是1号或者是第一个日期，显示月
    if (dateObj.date.getDate() === 1 || dateObj.date.getTime() === startDate.getTime()) {
        const monthLabel = dateBlock.createDiv();
        monthLabel.style.position = "absolute";
        monthLabel.style.bottom = "0";
        monthLabel.style.right = "0";
        monthLabel.style.fontSize = "0.6em";
        monthLabel.style.color = "var(--text-muted)";
        monthLabel.textContent = `${dateObj.date.getMonth() + 1}月`;
    }
});

// 在容器样式设置部分添加动画定义
const style = document.createElement('style');
style.textContent = `
    @keyframes perfect-pulse {
        0% {
            transform: scale(1);
            filter: brightness(1);
        }
        50% {
            transform: scale(1.05);
            filter: brightness(1.15) contrast(1.05);
        }
        100% {
            transform: scale(1);
            filter: brightness(1);
        }
    }
`;
document.head.appendChild(style);

/* 显示完成今天目标所需要的数据
 */

// 获取今天的日期和类型
const today = new Date();
const todayIndex = datesArray.findIndex(dateObj => dateObj.name === today.toISOString().slice(0, 10));

// 如果今天在阶段范围内，显示今日需求
if (todayIndex !== -1) {
    const todayType = datesArray[todayIndex].type;

    const requirementsContainer = container.createDiv();
    requirementsContainer.style.marginTop = "10px";
    requirementsContainer.style.marginBottom = "12px";
    requirementsContainer.style.padding = "8px 12px";
    requirementsContainer.style.fontSize = "0.9em";
    requirementsContainer.style.color = "var(--text-muted)";
    requirementsContainer.style.borderLeft = `3px solid ${TYPE_COLORS[todayType]}40`;
    requirementsContainer.style.backgroundColor = "var(--background-secondary)";
    requirementsContainer.style.borderRadius = "4px";

    const todayInfo = requirementsContainer.createDiv();
    todayInfo.style.marginBottom = "4px";
    todayInfo.innerHTML = `今日为 <span style="color: ${TYPE_COLORS[todayType]}; font-weight: 500;">${todayType}</span> 日，目标：`;

    const targetsList = requirementsContainer.createDiv();
    targetsList.style.display = "flex";
    targetsList.style.gap = "12px";
    targetsList.style.fontSize = "0.85em";

    const targets = [
        { name: "专注", key: "focus", color: TYPE_COLORS.focus },
        { name: "运动", key: "sport", color: TYPE_COLORS.sport },
        { name: "逍遥", key: "free", color: TYPE_COLORS.free },
    ];

    targets.forEach(target => {
        const targetSpan = targetsList.createSpan();
        targetSpan.innerHTML = `${target.name}: <span style="color: ${target.color}; font-weight: 500;">${PERFECT_TARGETS[todayType][target.key]}</span> 分钟`;
    });
}

function calcNextDailyType(arr, days = 1) {
    // 检查输入参数
    if (!Array.isArray(arr) || arr.length === 0) {
        throw new Error("日期类型数组不能为空");
    }

    // 预测类型的函数
    const predictType = (types) => {
        const lastType = types[types.length - 1]; // 获取最后一个日期类型
        const lastFourTypes = types.slice(-4); // 获取最新的四个日期类型
        const lastCIndex = types.lastIndexOf('C'); // 获取最后一个'C'的索引

        // 根据规则进行预测
        if (lastType === 'C') {
            return 'D'; // 规则3.1: 如果最后一个类型是C，则预测类型为D
        } else if (lastType === 'B' || lastType === 'D') {
            return 'A'; // 规则3.2: 如果最后一个类型是B或D，则预测类型为A
        } else if (lastType === 'A' && lastFourTypes.some(type => type !== 'A')) {
            return 'A'; // 规则3.3: 如果最后一个类型是A，并且最新的四个类型中有非A的存在，则预测类型为A
        } else if (lastFourTypes.every(type => type === 'A') && (lastCIndex === -1 || (lastFourTypes.length - lastCIndex >= 20))) {
            return 'C'; // 规则3.4: 如果最新的四个类型都是A，且距离最近的C的距离大于等于20，则预测类型为C
        } else if (lastFourTypes.every(type => type === 'A') && (lastCIndex !== -1 && (lastFourTypes.length - lastCIndex < 20))) {
            return 'B'; // 规则3.5: 如果最新的四个类型都是A，且最近的C距离小于20，则预测类型为B
        }

        return 'A'; // 默认返回类型为A
    };

    // 预测未来的日期类型
    const result = [];
    for (let i = 0; i < days; i++) {
        const nextType = predictType(arr); // 预测下一个类型
        result.push(nextType); // 将预测结果添加到结果数组中
        arr.push(nextType); // 更新数组以便下次预测
    }

    return result; // 返回预测结果
}