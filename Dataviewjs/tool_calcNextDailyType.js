// 在全局变量app上绑定一个新的方法calcNextDailyType
app.calcNextDailyType = function(arr, days = 1) {
    // 检查输入参数
    if (!Array.isArray(arr) || arr.length === 0) {
        throw new Error("日期类型数组不能为空");
    }

    // 预测类型的函数
    const predictType = (types) => {
        const lastType = types[types.length - 1]; // 获取最后一个日期类型
        const lastFourTypes = types.slice(-4); // 获取最新的四个日期类型
        const lastCIndex = types.lastIndexOf('C'); // 获取最后一个'C'的索引

        // 根据规则进行预测
        if (lastType === 'C') {
            return 'D'; // 规则3.1: 如果最后一个类型是C，则预测类型为D
        } else if (lastType === 'B' || lastType === 'D') {
            return 'A'; // 规则3.2: 如果最后一个类型是B或D，则预测类型为A
        } else if (lastType === 'A' && lastFourTypes.some(type => type !== 'A')) {
            return 'A'; // 规则3.3: 如果最后一个类型是A，并且最新的四个类型中有非A的存在，则预测类型为A
        } else if (lastFourTypes.every(type => type === 'A') && (lastCIndex === -1 || (lastFourTypes.length - lastCIndex >= 20))) {
            return 'C'; // 规则3.4: 如果最新的四个类型都是A，且距离最近的C的距离大于等于20，则预测类型为C
        } else if (lastFourTypes.every(type => type === 'A') && (lastCIndex !== -1 && (lastFourTypes.length - lastCIndex < 20))) {
            return 'B'; // 规则3.5: 如果最新的四个类型都是A，且最近的C距离小于20，则预测类型为B
        }

        return 'A'; // 默认返回类型为A
    };

    // 预测未来的日期类型
    const result = [];
    for (let i = 0; i < days; i++) {
        const nextType = predictType(arr); // 预测下一个类型
        result.push(nextType); // 将预测结果添加到结果数组中
        arr.push(nextType); // 更新数组以便下次预测
    }

    return result; // 返回预测结果
};
