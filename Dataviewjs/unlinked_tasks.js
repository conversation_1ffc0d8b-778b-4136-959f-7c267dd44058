// 获取日记文件夹中的所有文件
const journalPages = dv.pages('"Nexus/日记"');

// 过滤出符合条件的任务
const unlinkedTasks = journalPages.flatMap(page => {
    return page.file.tasks
        .filter(task => task.status.length > 0 && task.outlinks.length === 0 && !task.Flow)
        .map(task => ({
            file: page.file.link,
            task: task.text
        }));
});

// 使用dv.table显示结果
dv.table(
    ["文件", "任务"],
    unlinkedTasks.map(item => [item.file, item.task])
);