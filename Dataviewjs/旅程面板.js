/*
 * @描述: 旅程面板
 * @作者: roam1n
 * @版本: 0.0.1
 * @最后更新: 2025-03-24
 */

const TASK_TYPES = ['C', 'Z', 'H', 'Q', 'S'];
const TASK_TYPE_COLORS = {
    'C': '#81C784',
    'Z': '#64B5F6',
    'H': '#FFB74D',
    'Q': '#E57373',
    'S': '#54dfd4',
    // 专注类 - 青蓝紫色系
    "s": "#5C9EE5",  // 学习任务 - 蓝色 (调整为更柔和的蓝色)
    "t": "#7B7FC8",  // 实践任务 - 蓝紫色 (调整为更平衡的蓝紫色)
    "e": "#8A7AA8",  // 探索任务 - 紫灰色 (按要求调整为紫灰色)
    "a": "#9D8CB0",  // 思考任务 - 紫色 (调整明度与其他颜色更协调)
    "r": "#4A9B92",  // 练习任务 - 青色 (调整为更柔和的青色)
    
    // 逍遥类 - 红色系
    "B": "#D05353",  // 阅读任务 - 红色 (调整为更柔和的红色)
    "G": "#D9704A",  // 游戏任务 - 橙红色 (调整为更柔和的橙红色)
    "V": "#D88670",  // 影音任务 - 浅红色 (调整为更柔和的浅红色)
    
    // 其他类
    "o": "#607D8B",  // 其他任务 - 蓝灰色 (保持不变)
    "n": "#5FA06E",  // 种子任务 - 绿色 (调整为更柔和的绿色)
    
    // 保留原有的j类别以防万一
    "j": "#808080",  // 杂项任务 - 灰色
};
const FLOW_TO_TASK_TYPE = {
    '学习': 's',
    '实践': 't',
    '探索': 'e',
    '思考': 'a',
    '复盘': 'a',
    '练习': 'r',
    '循环': 'r',
    '阅读': 'B',
    '订阅': 'B',
    '游戏': 'G',
    '影音': 'V',
    '听书': 'V',
    '其他': 'o',
    '种子': 'n',
}

const TODAY = new Date();
const CURRENT_JOURNEY = dv.current();
const STEPS = dv.pages(`"Nexus/印记/${CURRENT_JOURNEY.file.name}"`).sort(p => p.file.name);

const wait_steps = [];
const start_normal = [];
const expect_normal = [];
const expect_important = [];
const expect_urgent = [];

STEPS.forEach(page => {
    const stepName = page.file.name.replace('.md', '');
    const stepType = page.流转;
    const stepPath = page.file.path;

    // 如果step没有任务，则加入等待列表
    if (page.file.tasks.length === 0) {
        wait_steps.push({stepName, stepType, stepPath});
        return;
    }

    page.file.tasks.forEach(task => {
        const taskType = task.status;
        // 匹配日期和描述的正则
        const pattern = /\[\[(\d{4})-(\d{2})-(\d{2})\]\]\s*(.*)/;
        const match = task.text.trim().match(pattern);
        const path = `Nexus/印记/${CURRENT_JOURNEY.file.name}/${stepName}`

        if (match) {
            const year = match[1]
            const date = match[2] + match[3];
            let text = match[4] || ''; // 如果没有描述则为空字符串

            // 提取专注信息和其他标签
            let 专注 = null;
            // 匹配 [标签::值] 格式，提取专注信息
            const tagPattern = /\[([^:]+)::([^\]]+)\]/g;
            let tagMatch;
            while ((tagMatch = tagPattern.exec(text)) !== null) {
                const tagName = tagMatch[1];
                const tagValue = tagMatch[2];
                if (tagName === '专注') {
                    专注 = tagValue;
                }
            }
            // 移除所有标签，只保留纯文本
            text = text.replace(/\s*\[[^:]+::[^\]]+\]/g, '').trim();

            if (['C', 'Z'].includes(taskType)) {
                start_normal.push({year, date, text, stepName, stepType: FLOW_TO_TASK_TYPE[stepType], taskType, path, 专注});
            } else if (['H', 'Q'].includes(taskType)) {
                if (TODAY.getFullYear() < year || TODAY.getMonth() + 1 < parseInt(match[2]) || TODAY.getDate() <= parseInt(match[3])) {
                    expect_urgent.push({year, date, text, stepName, stepType: FLOW_TO_TASK_TYPE[stepType], taskType, path, 专注});
                } else {
                    expect_normal.push({year, date, text, stepName, stepType: FLOW_TO_TASK_TYPE[stepType], taskType, path, 专注});
                }
            }
        } else {
            // 对于没有日期的任务，也需要处理标签
            let text = task.text;
            let 专注 = null;
            const tagPattern = /\[([^:]+)::([^\]]+)\]/g;
            let tagMatch;
            while ((tagMatch = tagPattern.exec(text)) !== null) {
                const tagName = tagMatch[1];
                const tagValue = tagMatch[2];
                if (tagName === '专注') {
                    专注 = tagValue;
                }
            }
            text = text.replace(/\s*\[[^:]+::[^\]]+\]/g, '').trim();

            if (['C', 'Z', 'S'].includes(taskType)) {
                start_normal.push({year: null, date: null, text, stepName, stepType: FLOW_TO_TASK_TYPE[stepType], taskType, path, 专注});
            } else if (['H', 'Q'].includes(taskType)) {
                expect_normal.push({year: null, date: null, text, stepName, stepType: FLOW_TO_TASK_TYPE[stepType], taskType, path, 专注});
            }
        }
    });
});

CURRENT_JOURNEY.file.tasks.forEach(task => {
    const taskType = task.status;
    const pattern = /\[\[(\d{4})-(\d{2})-(\d{2})\]\]\s*(.*)/;
    const match = task.text.trim().match(pattern);
    const path = null;

    if (match) {
        const year = match[1];
        const date = match[2] + match[3];
        const text = match[4].replace(/\s*\[[^:]+::[^\]]+\]/g, '').trim() || '';
        const 专注  = task.专注 || null;

        if (['H', 'Q'].includes(taskType)) {
            if (TODAY.getFullYear() < year || TODAY.getMonth() + 1 < parseInt(match[2]) || TODAY.getDate() <= parseInt(match[3])) {
                expect_urgent.push({year, date, text, stepName: null, stepType: null, taskType, path, 专注});
            } else {
                expect_important.push({year, date, text, stepName: null, stepType: null, taskType, path, 专注});
            }
        } else if (['C', 'Z'].includes(taskType)) {
            start_normal.push({year, date, text, stepName: null, stepType: null, taskType, path, 专注});
        }
    } else {
        // 对于没有日期的任务，也需要处理标签
        let text = task.text.replace(/\s*\[[^:]+::[^\]]+\]/g, '').trim();
        let 专注 = task.专注 || null;

        if (['H', 'Q', 'S'].includes(taskType)) {
            expect_important.push({year: null, date: null, text, stepName: null, stepType: null, taskType, path, 专注});
        }
    }
});


const container = dv.container;
container.style.margin = "2px auto";
container.style.position = "relative";
container.style.fontSize = "14px";

// 🐳 旅程面板

const journeyPanel = document.createElement("div");
journeyPanel.className = 'journey-panel';
journeyPanel.style.width = "100%";
journeyPanel.style.height = "100%";
journeyPanel.style.position = "relative";
journeyPanel.style.borderRadius = "5px";
journeyPanel.style.display = "flex";
journeyPanel.style.flexDirection = "column";
journeyPanel.style.gap = "12px";
journeyPanel.style.padding = "12px";
container.appendChild(journeyPanel);

// 添加样式
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse-subtle {
        0% { transform: translateX(0); }
        50% { transform: translateX(2px); }
        100% { transform: translateX(0); }
    }
    .wait-step:hover {
        background-color: var(--background-secondary-alt);
        transform: translateX(2px);
    }
    .cm-contentContainer.cm-contentContainer>.cm-content>div:has(.journey-panel) {
        max-width: 50rem !important;
        width: 90% !important; 
    }`;
container.appendChild(style);

// 标题区域
const titleSection = document.createElement("div");
titleSection.style.display = "flex";
titleSection.style.justifyContent = "space-between";
titleSection.style.alignItems = "center";
titleSection.style.borderRadius = "4px";
titleSection.style.paddingBottom = "6px";
titleSection.style.borderBottom = "1px solid var(--background-modifier-border)";
journeyPanel.appendChild(titleSection);

// 旅程标题
const journeyTitle = document.createElement("div");
journeyTitle.textContent = "旅程面板";
journeyTitle.style.fontWeight = "600";
journeyTitle.style.color = "var(--text-normal)";
journeyTitle.style.opacity = "0.9";
journeyTitle.style.fontSize = "0.9em";
titleSection.appendChild(journeyTitle);

// 添加统计信息
const statsInfo = document.createElement("div");
statsInfo.textContent = `共${STEPS.length}个印记`;
statsInfo.style.fontSize = "0.7em";
statsInfo.style.color = "var(--text-muted)";
statsInfo.style.opacity = "0.8";
titleSection.appendChild(statsInfo);

// 1. 等待步骤部分
if (wait_steps.length > 0) {
    const waitStepsSection = document.createElement("div");
    waitStepsSection.style.display = "flex";
    waitStepsSection.style.flexDirection = "column";
    waitStepsSection.style.gap = "8px";
    waitStepsSection.style.marginTop = "8px";
    waitStepsSection.style.backgroundColor = "var(--background-secondary)";
    waitStepsSection.style.borderRadius = "4px";
    waitStepsSection.style.padding = "10px";
    journeyPanel.appendChild(waitStepsSection);

    // 创建等待步骤列表
    const waitStepsList = document.createElement("div");
    waitStepsList.style.display = "flex";
    waitStepsList.style.flexDirection = "column";
    waitStepsList.style.gap = "4px";
    waitStepsSection.appendChild(waitStepsList);

    // 处理等待步骤数据
    wait_steps.forEach(step => {
        const waitStepItem = document.createElement("div");
        waitStepItem.className = "wait-step";
        waitStepItem.style.display = "flex";
        waitStepItem.style.alignItems = "center";
        waitStepItem.style.gap = "8px";
        waitStepItem.style.backgroundColor = "var(--background-primary)";
        waitStepItem.style.borderRadius = "4px";
        waitStepItem.style.padding = "6px 8px";
        waitStepItem.style.transition = "all 0.2s ease";
        waitStepItem.style.cursor = "pointer";

        // 步骤类型图标
        const iconDiv = document.createElement("span");
        iconDiv.setAttribute("data-icon", step.stepType || "o");
        iconDiv.textContent = step.stepType || "o";
        iconDiv.style.color = TASK_TYPE_COLORS[step.stepType] || TASK_TYPE_COLORS['o'];
        iconDiv.style.fontSize = "0.7em";
        waitStepItem.appendChild(iconDiv);

        // 步骤名称
        const stepNameDiv = document.createElement("a");
        stepNameDiv.setAttribute("data-href", step.stepPath);
        stepNameDiv.className = "internal-link";
        stepNameDiv.href = step.stepPath;
        stepNameDiv.textContent = step.stepName;
        stepNameDiv.style.flex = "1";
        stepNameDiv.style.color = "var(--text-normal)";
        stepNameDiv.style.fontSize = "0.9em";
        waitStepItem.appendChild(stepNameDiv);

        waitStepsList.appendChild(waitStepItem);
    });
}

// 2. 时间线部分
if (!start_normal.length && !expect_normal.length && !expect_important.length && !expect_urgent.length) {
    return;
}

const timelinesContainer = document.createElement("div");
timelinesContainer.style.display = "flex";
timelinesContainer.style.gap = "16px";
timelinesContainer.style.marginTop = "4px";
journeyPanel.appendChild(timelinesContainer);

dv.view('印记时间线', {container: timelinesContainer, title: 'Start', normal: start_normal})
dv.view('印记时间线', {container: timelinesContainer, title: 'Expect', normal: expect_normal, important: expect_important, urgent: expect_urgent, direction: 'right', isasc: true})
