/*
 * @描述: 卡片式内容展示
 * @作者: roam1n
 * @版本: 0.0.2
 * @最后更新: 2025-01-22
 */

// 创建容器
const container = dv.container;
container.style.width = "100%";
container.style.display = "flex";
container.style.flexWrap = "wrap";
container.style.gap = "20px";
container.style.padding = "10px";
container.style.fontSize = "14px";
container.classList.add('cards-container')

// 获取当前页面检索的内容
const limit = input?.limit ?? 30;
const DEFAULT_PAGES = dv.pages('"Nexus/日记"').sort(p=>p.file.name, 'desc');
let pages = input?.pages ?? DEFAULT_PAGES;
pages = pages.filter(p=>p.file.name !== dv.current().file.name ).limit(limit);

// 为每个页面创建列容器
pages.forEach(page => {
    const column = document.createElement("div");
    column.style.flex = "1 1 400px"; // 最小宽度400px，允许增长和收缩
    column.style.minWidth = "300px"; // 确保不会太窄
    column.style.display = "flex";
    column.style.flexDirection = "column";

    // 创建标题
    const title = document.createElement("a");
    title.textContent = page.file.name;
    title.style.margin = "0";
    title.style.color = "var(--text-normal)";
    title.style.borderBottom = "2px solid var(--text-accent)";
    title.style.paddingBottom = "8px";
    title.classList.add('internal-link')
    title.dataset['href'] = page.file.path;
    title.href = page.file.path;
    column.appendChild(title);

    // 创建内容容器
    const contentWrapper = document.createElement("div");
    contentWrapper.style.backgroundColor = "var(--background-primary)";
    contentWrapper.style.borderRadius = "10px";
    contentWrapper.style.padding = "0 16px 16px";
    contentWrapper.style.boxShadow = "0 2px 8px rgba(0, 0, 0, 0.1)";
    contentWrapper.style.transition = "transform 0.2s ease";
    contentWrapper.style.marginTop = "10px";
    contentWrapper.style.maxHeight = "300px";
    contentWrapper.style.overflow = "scroll";
    contentWrapper.style.flex = "1";

    // 添加悬停效果
    contentWrapper.addEventListener('mouseenter', () => {
        contentWrapper.style.transform = "translateY(-5px)";
    });
    contentWrapper.addEventListener('mouseleave', () => {
        contentWrapper.style.transform = "translateY(0)";
    });

    // Frontmatter
    const frontmatter = document.createElement("div");
    frontmatter.style.fontSize = "0.8em";
    frontmatter.style.color = "var(--text-muted)";
    frontmatter.style.borderTop = "1px solid var(--background-modifier-border)";
    frontmatter.style.paddingTop = "10px";
    frontmatter.style.marginTop = "auto";

    // 获取并显示frontmatter内容
    const frontmatterContent = Object.entries(page)
        .filter(([key]) => !['file', 'position'].includes(key))
        .map(([key, value]) => `${key}: ${value}`)
        .join(' ');
    frontmatter.innerHTML = frontmatterContent;
    // 渲染文件内容
    app.vault.cachedRead(app.vault.getFileByPath(page.file.path)).then(fileContent => {
        const content = dv.paragraph(fileContent.replace(/```dvjs[\s\S]*?```/g, '').trim());
        content.style.color = "var(--text-normal)";
        content.style.fontSize = "14px";
        contentWrapper.appendChild(content);

        column.appendChild(contentWrapper);
        container.appendChild(column);
        if (frontmatterContent) {
            column.appendChild(frontmatter);
        }
    });

});
