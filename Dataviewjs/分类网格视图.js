/*
 * @描述: 展示主题分类列表
 * @作者: roam1n
 * @版本: 0.0.2
 * @最后更新: 2025-04-17
 */

const folder = input?.folder || "Nexus/主题";
const allPages = dv.pages(`"${folder}"`).sort(p=>p.file.name, 'asc').sort(p=>p.完成, 'asc');

// object 以分类为key 以文件为value
const categories = {};

allPages.forEach(p=>{
    const category = p.up[0].path.split('/').pop().replace('.md', '');
    categories[category] = categories[category] || [];
    categories[category].push(p.file.path);
});

// 创建容器
const container = dv.container;
container.className = "cgv-outer-container";

// 创建主容器
const section = document.createElement("div");
container.appendChild(section);
section.id = "cgv-grid-section";

// 遍历categories
Object.keys(categories).forEach(category => {
    const card = document.createElement("div");
    section.appendChild(card);
    card.className = "cgv-card";
    
    // 卡片基础样式
    // card.style.borderRadius = "12px";
    // card.style.padding = "16px";
    // card.style.backdropFilter = "blur(10px)";
    // card.style.border = "1px solid var(--background-modifier-border)";
    // card.style.transition = "all 0.3s ease";
    
    // 卡片悬浮效果 (removed, handled by CSS :hover)
    // card.addEventListener('mouseenter', () => {
    //     card.style.transform = "translateY(-2px)";
    //     card.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.2)";
    // });
    
    // card.addEventListener('mouseleave', () => {
    //     card.style.transform = "translateY(0)";
    //     card.style.boxShadow = "none";
    // });

    const cardTitle = document.createElement("div");
    card.appendChild(cardTitle);
    cardTitle.className = "cgv-card-title";
    // cardTitle.style.fontSize = "1.1em";
    // cardTitle.style.fontWeight = "600";
    // cardTitle.style.color = "var(--text-normal)";
    // cardTitle.style.marginBottom = "12px";
    // cardTitle.style.paddingBottom = "8px";
    // cardTitle.style.borderBottom = "1px solid var(--background-modifier-border)";
    cardTitle.textContent = category;

    const cardContent = document.createElement("div");
    card.appendChild(cardContent);
    cardContent.className = "cgv-card-content";
    // cardContent.style.display = "flex";
    // cardContent.style.flexWrap = "wrap";
    // cardContent.style.gap = "8px";

    categories[category].forEach(file => {
        const fileLink = document.createElement("a");
        cardContent.appendChild(fileLink);
        fileLink.href = file;
        fileLink.className = "internal-link cgv-file-link"; // Added cgv-file-link
        fileLink.dataset['href'] = file;
        fileLink.textContent = file.split('/').pop().replace('.md', '');
        
        // 链接样式 (removed, handled by .cgv-file-link class)
        // fileLink.style.background = "var(--background-modifier-border)";
        // fileLink.style.padding = "6px 12px";
        // fileLink.style.borderRadius = "6px";
        // fileLink.style.fontSize = "0.9em";
        // fileLink.style.color = "var(--text-normal)";
        // fileLink.style.textDecoration = "none";
        // fileLink.style.transition = "all 0.2s ease";
        
        // 链接悬浮效果 (removed, handled by CSS :hover)
        // fileLink.addEventListener('mouseenter', () => {
        //     fileLink.style.background = "var(--background-modifier-border)";
        //     fileLink.style.color = "var(--text-normal)";
        //     fileLink.style.transform = "scale(1.02)";
        // });
        
        // fileLink.addEventListener('mouseleave', () => {
        //     fileLink.style.background = "var(--background-modifier-border)";
        //     fileLink.style.color = "var(--text-normal)";
        //     fileLink.style.transform = "scale(1)";
        // });
    });
});
