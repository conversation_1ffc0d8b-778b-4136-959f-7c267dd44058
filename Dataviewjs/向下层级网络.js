/*
 * @描述: 展示文件的三层下级连接网络
 * @作者: roam1n
 * @版本: 0.0.2
 * @最后更新: 2025-01-27
 */

// 获取输入参数
const folders = input?.folders || ["Nexus"];
const display_frontmatter = input?.display || null;
const currentFile = dv.current().file;

// 特别的文件关系 以Path为key 以Frontmatter为value 符合Path的文件其up为value
const specialRelations = {
    "Nexus/旅程": "旅程",
    "Nexus/愿望": "愿望",
    "Nexus/主题": "主题",
}

// 查找下级文件函数
function findChildFiles(parentLink, allPages) {
    const pathStart = parentLink.path.split("/").slice(0, 2).join("/");
    const relation = specialRelations[pathStart];
    return allPages.filter(page => {
        if (!page[relation] && !page.up) return false;
        return Array.isArray(page[relation] || page.up)
            ? (page[relation] || page.up).find(l=> l.path == parentLink.path)
            : (page[relation] || page.up).path === parentLink.path;
    });
}

// 构建层级数据
function buildHierarchy() {
    // 获取指定文件夹下的所有页面
    // const allPages = folders.reduce((acc, folder) => {
    //     const pages = dv.pages(`"${folder}"`).sort(p=>p.file.ctime, 'desc');
    //     return [...acc, ...pages];
    // }, []);

    const allPages = dv.pages(`"${folders.join('" or "')}"`).sort(p=>p.file.name, 'asc').sort(p=>p.完成, 'asc');
    console.log(allPages);

    // 存储所有层级关系组
    const hierarchyGroups = [];

    // 第一层 - 直接子级
    const children = findChildFiles(currentFile.link, allPages);

    children.forEach(child => {
        // 为每个子级创建一个层级组
        const group = {
            child: child.file.link + (child[display_frontmatter] ? ` ${child[display_frontmatter]}` : ""),
            grandChildren: [] // 每个孙子都是一个对象，包含自己的链接和重孙子数组
        };

        // 收集第二层 - 孙子级
        const grandChildren = findChildFiles(child.file.link, allPages);
        grandChildren.forEach(grandChild => {
            // 收集第三层 - 重孙子级
            const greatGrandChildren = findChildFiles(grandChild.file.link, allPages)
                .map(greatGrandChild => greatGrandChild.file.link);

            // 将孙子及其重孙子作为一个整体存储
            group.grandChildren.push({
                link: grandChild.file.link,
                greatGrandChildren: greatGrandChildren
            });
        });

        hierarchyGroups.push(group);
    });

    // 将层级组转换为表格行数据
    const hierarchyData = [];
    hierarchyGroups.forEach(group => {
        // 如果没有孙子
        if (group.grandChildren.length === 0) {
            hierarchyData.push([group.child, "", ""]);
            return;
        }

        // 处理第一个孙子及其重孙子
        const firstGrandChild = group.grandChildren[0];
        const firstRow = [
            group.child,
            firstGrandChild.link,
            firstGrandChild.greatGrandChildren[0] || ""
        ];
        hierarchyData.push(firstRow);

        // 处理第一个孙子的剩余重孙子
        for (let i = 1; i < firstGrandChild.greatGrandChildren.length; i++) {
            hierarchyData.push(["", "<span style='color: #808080;'>↳</span>", firstGrandChild.greatGrandChildren[i]]);
        }

        // 处理剩余的孙子及其重孙子
        for (let i = 1; i < group.grandChildren.length; i++) {
            const grandChild = group.grandChildren[i];
            // 添加孙子
            hierarchyData.push(["<span style='color: #808080;'>↳</span>", grandChild.link, grandChild.greatGrandChildren[0] || ""]);

            // 添加该孙子的所有剩余重孙子
            for (let j = 1; j < grandChild.greatGrandChildren.length; j++) {
                hierarchyData.push(["", "<span style='color: #808080;'>↳</span>", grandChild.greatGrandChildren[j]]);
            }
        }
    });

    // 特殊处理：如果display_frontmatter是灵石，则计算所有子级的灵石总数
    if (display_frontmatter && display_frontmatter === '灵石') {
        const sum = children.reduce((acc, child) => acc + child[display_frontmatter], 0);
        hierarchyData.push([sum, "", ""]);

        app.fileManager.processFrontMatter(app.workspace.getActiveFile(), (fm) => {
            fm.灵石 = parseFloat(sum.toFixed(1))
        });
    }

    return hierarchyData;
}

// 生成表格数据
const hierarchyData = buildHierarchy();

// 使用 dv.paragraph 和 dv.markdownTable 显示结果
if (hierarchyData.length > 0) {
    dv.paragraph(dv.markdownTable(
        ["...", "..", "."],
        hierarchyData
    ));
} else {
    dv.paragraph("*没有找到下级文件*");
}