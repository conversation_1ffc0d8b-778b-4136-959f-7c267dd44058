/*
 * @描述: 旅程面板
 * @作者: roam1n
 * @版本: 0.0.1
 * @最后更新: 2025-03-24
 */

const TASK_TYPES = ['C', 'Z', 'H', 'Q', 'S'];
const TASK_TYPE_COLORS = {
    'C': '#81C784',
    'Z': '#64B5F6',
    'H': '#FFB74D',
    'Q': '#E57373',
    'S': '#54dfd4',
    // 专注类 - 青蓝紫色系
    "s": "#5C9EE5",  // 学习任务 - 蓝色 (调整为更柔和的蓝色)
    "t": "#7B7FC8",  // 实践任务 - 蓝紫色 (调整为更平衡的蓝紫色)
    "e": "#8A7AA8",  // 探索任务 - 紫灰色 (按要求调整为紫灰色)
    "a": "#9D8CB0",  // 思考任务 - 紫色 (调整明度与其他颜色更协调)
    "r": "#4A9B92",  // 练习任务 - 青色 (调整为更柔和的青色)
    
    // 逍遥类 - 红色系
    "B": "#D05353",  // 阅读任务 - 红色 (调整为更柔和的红色)
    "G": "#D9704A",  // 游戏任务 - 橙红色 (调整为更柔和的橙红色)
    "V": "#D88670",  // 影音任务 - 浅红色 (调整为更柔和的浅红色)
    
    // 其他类
    "o": "#607D8B",  // 其他任务 - 蓝灰色 (保持不变)
    "n": "#5FA06E",  // 种子任务 - 绿色 (调整为更柔和的绿色)
    
    // 保留原有的j类别以防万一
    "j": "#808080",  // 杂项任务 - 灰色
};
const FLOW_TO_TASK_TYPE = {
    '学习': 's',
    '实践': 't',
    '探索': 'e',
    '思考': 'a',
    '复盘': 'a',
    '练习': 'r',
    '循环': 'r',
    '阅读': 'B',
    '订阅': 'B',
    '游戏': 'G',
    '影音': 'V',
    '听书': 'V',
    '其他': 'o',
    '种子': 'n',
}

const TODAY = new Date();
const CURRENT_JOURNEY = dv.current();
const STEPS = dv.pages(`"Nexus/印记/${CURRENT_JOURNEY.file.name}"`).sort(p => p.file.name);

const wait_steps = [];
const start_normal = [];
const expect_normal = [];
const expect_important = [];
const expect_urgent = [];

STEPS.forEach(page => {
    const stepName = page.file.name.replace('.md', '');
    const stepType = page.流转;

    // 如果step没有任务，则加入等待列表
    if (page.file.tasks.length === 0) {
        wait_steps.push({stepName, stepType});
        return;
    }

    page.file.tasks.forEach(task => {
        const taskType = task.status;
        // 匹配日期和描述的正则
        const pattern = /\[\[(\d{4})-(\d{2})-(\d{2})\]\]\s*(.*)/;
        const match = task.text.trim().match(pattern);
        
        if (match) {
            const year = match[1]
            const date = match[2] + match[3];
            const text = match[4] || ''; // 如果没有描述则为空字符串

            if (['C', 'Z'].includes(taskType)) {
                start_normal.push({year, date, text, stepName, stepType: FLOW_TO_TASK_TYPE[stepType], taskType});
            } else if (['H', 'Q'].includes(taskType)) {
                if (TODAY.getFullYear() < year || TODAY.getMonth() + 1 < parseInt(match[2]) || TODAY.getDate() <= parseInt(match[3])) {
                    expect_urgent.push({year, date, text, stepName, stepType: FLOW_TO_TASK_TYPE[stepType], taskType});
                } else {
                    expect_normal.push({year, date, text, stepName, stepType: FLOW_TO_TASK_TYPE[stepType], taskType}); 
                }
            }
        } else {
            if (['C', 'Z', 'S'].includes(stepType)) {
                start_normal.push({year: null, date: null, text: task.text, stepName, stepType: FLOW_TO_TASK_TYPE[stepType], taskType});
            } else if (['H', 'Q'].includes(stepType)) {
                expect_normal.push({year: null, date: null, text: task.text, stepName, stepType: FLOW_TO_TASK_TYPE[stepType], taskType});
            }
        }
    });
});

CURRENT_JOURNEY.file.tasks.forEach(task => {
    const taskType = task.status;
    const pattern = /\[\[(\d{4})-(\d{2})-(\d{2})\]\]\s*(.*)/;
    const match = task.text.trim().match(pattern);
    
    if (match) {
        const year = match[1];
        const date = match[2] + match[3];
        const text = match[4] || '';

        if (['H', 'Q'].includes(taskType)) {
            if (TODAY.getFullYear() < year || TODAY.getMonth() + 1 < parseInt(match[2]) || TODAY.getDate() <= parseInt(match[3])) {
                expect_urgent.push({year, date, text, stepName: null, stepType: null, taskType});
            } else {
                expect_important.push({year, date, text, stepName: null, stepType: null, taskType});
            }
        } else {
            if (taskType === 'S') {
                start_normal.push({year: null, date: null, text: task.text, stepName: null, stepType: null, taskType});
            } else if (['H', 'Q'].includes(taskType)) {
                expect_important.push({year: null, date: null, text: task.text, stepName: null, stepType: null, taskType});
            }
        }
    }
});

const container = dv.container;
container.style.width = "100%";
container.style.height = "100%";
container.style.position = "relative";
container.style.backgroundColor = "var(--background-primary)";
container.style.marginBottom = "12px";

// 🐳 旅程面板

const journeyPanel = document.createElement("div");
journeyPanel.style.width = "100%";
journeyPanel.style.height = "100%";
journeyPanel.style.position = "relative";
journeyPanel.style.borderRadius = "5px";
journeyPanel.style.display = "flex";
journeyPanel.style.flexDirection = "column";
journeyPanel.style.gap = "12px";
journeyPanel.style.padding = "12px";
container.appendChild(journeyPanel);

// 添加样式
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse-subtle {
        0% { transform: translateX(0); }
        50% { transform: translateX(2px); }
        100% { transform: translateX(0); }
    }
    
    .timeline-item:hover {
        background-color: var(--background-secondary-alt);
        transform: translateX(2px);
    }
    
    .timeline-item[data-type="urgent"] {
        border-left: 3px solid #E57373;
    }
    
    .timeline-item[data-type="important"] {
        border-left: 3px solid #FFB74D;
    }
    
    .timeline-dot {
        position: absolute;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: var(--text-muted);
        z-index: 1;
    }
    
    .timeline-line {
        position: absolute;
        width: 2px;
        height: 100%;
        background-color: var(--background-modifier-border);
        z-index: 0;
    }
    
    .wait-step:hover {
        background-color: var(--background-secondary-alt);
        transform: translateX(2px);
    }
    
    .year-marker {
        font-weight: bold;
        color: var(--text-accent);
        padding: 4px 0;
        margin-top: 8px;
    }
    
    .date-marker {
        color: var(--text-muted);
        font-size: 0.8em;
        padding: 2px 0;
    }
`;
container.appendChild(style);

// 标题区域
const titleSection = document.createElement("div");
titleSection.style.display = "flex";
titleSection.style.justifyContent = "space-between";
titleSection.style.alignItems = "center";
titleSection.style.borderRadius = "4px";
titleSection.style.paddingBottom = "6px";
titleSection.style.borderBottom = "1px solid var(--background-modifier-border)";
journeyPanel.appendChild(titleSection);

// 旅程标题
const journeyTitle = document.createElement("div");
journeyTitle.textContent = "旅程面板";
journeyTitle.style.fontWeight = "600";
journeyTitle.style.color = "var(--text-normal)";
journeyTitle.style.opacity = "0.9";
journeyTitle.style.fontSize = "0.9em";
titleSection.appendChild(journeyTitle);

// 添加统计信息
const statsInfo = document.createElement("div");
statsInfo.textContent = `共${STEPS.length}个印记`;
statsInfo.style.fontSize = "0.7em";
statsInfo.style.color = "var(--text-muted)";
statsInfo.style.opacity = "0.8";
titleSection.appendChild(statsInfo);

// 1. 等待步骤部分
if (wait_steps.length > 0) {
    const waitStepsSection = document.createElement("div");
    waitStepsSection.style.display = "flex";
    waitStepsSection.style.flexDirection = "column";
    waitStepsSection.style.gap = "8px";
    waitStepsSection.style.marginTop = "8px";
    waitStepsSection.style.backgroundColor = "var(--background-secondary)";
    waitStepsSection.style.borderRadius = "4px";
    waitStepsSection.style.padding = "10px";
    journeyPanel.appendChild(waitStepsSection);

    // 创建等待步骤列表
    const waitStepsList = document.createElement("div");
    waitStepsList.style.display = "flex";
    waitStepsList.style.flexDirection = "column";
    waitStepsList.style.gap = "4px";
    waitStepsSection.appendChild(waitStepsList);

    // 处理等待步骤数据
    wait_steps.forEach(step => {
        const waitStepItem = document.createElement("div");
        waitStepItem.className = "wait-step";
        waitStepItem.style.display = "flex";
        waitStepItem.style.alignItems = "center";
        waitStepItem.style.gap = "8px";
        waitStepItem.style.backgroundColor = "var(--background-primary)";
        waitStepItem.style.borderRadius = "4px";
        waitStepItem.style.padding = "6px 8px";
        waitStepItem.style.transition = "all 0.2s ease";
        waitStepItem.style.cursor = "pointer";

        // 步骤类型图标
        const iconDiv = document.createElement("span");
        iconDiv.setAttribute("data-icon", step.stepType || "o");
        iconDiv.textContent = step.stepType || "o";
        waitStepItem.appendChild(iconDiv);

        // 步骤名称
        const stepNameDiv = document.createElement("div");
        stepNameDiv.textContent = step.stepName;
        stepNameDiv.style.flex = "1";
        stepNameDiv.style.color = "var(--text-normal)";
        stepNameDiv.style.fontSize = "0.9em";
        waitStepItem.appendChild(stepNameDiv);

        waitStepsList.appendChild(waitStepItem);
    });
}

// 2. 时间线部分
const timelinesContainer = document.createElement("div");
timelinesContainer.style.display = "flex";
timelinesContainer.style.gap = "16px";
timelinesContainer.style.marginTop = "4px";
journeyPanel.appendChild(timelinesContainer);

// 创建时间线列表函数
function createTimelineList(title, normal = [], important = [], urgent = [], isLeft = true, isASC = true) {
    const timelineSection = document.createElement("div");
    timelineSection.style.flex = "1";
    timelineSection.style.display = "flex";
    timelineSection.style.flexDirection = "column";
    timelineSection.style.gap = "8px";
    timelineSection.style.backgroundColor = "var(--background-secondary)";
    timelineSection.style.borderRadius = "4px";
    timelineSection.style.padding = "10px";
    timelineSection.style.position = "relative";
    timelineSection.style.maxHeight = "calc(100vh - 200px)";
    timelineSection.style.overflowY = "auto";

    // 时间线标题
    const timelineTitle = document.createElement("div");
    timelineTitle.textContent = title;
    timelineTitle.style.fontWeight = "bold";
    timelineTitle.style.marginBottom = "6px";
    timelineTitle.style.color = "var(--text-normal)";
    timelineTitle.style.opacity = "0.9";
    if (isLeft) {
        timelineTitle.style.textAlign = "left";
        timelineTitle.style.marginLeft = "calc(80px - 3em)";
    } else {
        timelineTitle.style.textAlign = "right";
        timelineTitle.style.marginRight = "calc(80px - 3em)";
    }
    timelineSection.appendChild(timelineTitle);

    // 创建时间线容器
    const timelineList = document.createElement("div");
    timelineList.style.display = "flex";
    timelineList.style.flexDirection = "column";
    timelineList.style.gap = "2px";
    timelineList.style.position = "relative";
    timelineSection.appendChild(timelineList);

    // 创建时间线
    const timelineLine = document.createElement("div");
    timelineLine.className = "timeline-line";
    timelineLine.style.left = isLeft ? "80px" : "calc(100% - 80px)";
    timelineList.appendChild(timelineLine);

    // 合并并排序所有项目
    let allItems = [...urgent, ...important, ...normal];
    
    // 按日期排序
    allItems.sort((a, b) => {
        // 没有日期的项目放在最后
        if (!a.year && !a.date) return isASC ? 1 : -1;
        if (!b.year && !b.date) return isASC ? -1 : 1;
        
        // 按年份排序
        if (a.year !== b.year) {
            return isASC ? a.year - b.year : b.year - a.year;
        }
        
        // 按日期排序
        return isASC ? a.date - b.date : b.date - a.date;
    });

    // 跟踪已显示的年份和日期
    let lastYear = null;
    let lastDate = null;

    // 渲染时间线项目
    allItems.forEach((item, index) => {
        // 检查是否需要显示年份标记
        if (item.year && item.year !== lastYear) {
            const yearMarker = document.createElement("div");
            yearMarker.className = "year-marker";
            yearMarker.textContent = item.year;
            yearMarker.style.textAlign = isLeft ? "left" : "right";
            yearMarker.style.paddingLeft = isLeft ? "90px" : "0";
            yearMarker.style.paddingRight = isLeft ? "0" : "90px";
            timelineList.appendChild(yearMarker);
            lastYear = item.year;
            lastDate = null; // 重置日期，因为新的年份
        }

        // 检查是否需要显示日期标记
        if (item.date && item.date !== lastDate) {
            const dateMarker = document.createElement("div");
            dateMarker.className = "date-marker";
            dateMarker.textContent = `${item.date.substring(0, 2)}-${item.date.substring(2, 4)}`;
            dateMarker.style.textAlign = isLeft ? "left" : "right";
            dateMarker.style.paddingLeft = isLeft ? "90px" : "0";
            dateMarker.style.paddingRight = isLeft ? "0" : "90px";
            timelineList.appendChild(dateMarker);
            lastDate = item.date;
        }

        // 创建时间线项目
        const timelineItem = document.createElement("div");
        timelineItem.className = "timeline-item";
        timelineItem.style.display = "flex";
        timelineItem.style.alignItems = "center";
        timelineItem.style.padding = "6px 8px";
        timelineItem.style.backgroundColor = "var(--background-primary)";
        timelineItem.style.borderRadius = "4px";
        timelineItem.style.margin = "2px 0";
        timelineItem.style.transition = "all 0.2s ease";
        timelineItem.style.position = "relative";
        
        // 设置项目类型
        if (urgent.includes(item)) {
            timelineItem.setAttribute("data-type", "urgent");
        } else if (important.includes(item)) {
            timelineItem.setAttribute("data-type", "important");
        } else {
            timelineItem.setAttribute("data-type", "normal");
        }

        // 创建时间点
        const timelineDot = document.createElement("div");
        timelineDot.className = "timeline-dot";
        timelineDot.style.left = isLeft ? "80px" : "calc(100% - 80px)";
        timelineItem.appendChild(timelineDot);

        // 时间显示
        if (item.date) {
            const timeDiv = document.createElement("div");
            timeDiv.style.width = "70px";
            timeDiv.style.fontSize = "0.8em";
            timeDiv.style.color = "var(--text-muted)";
            timeDiv.style.textAlign = isLeft ? "right" : "left";
            timeDiv.style.paddingRight = isLeft ? "15px" : "0";
            timeDiv.style.paddingLeft = isLeft ? "0" : "15px";
            timeDiv.style.order = isLeft ? 0 : 2;
            timeDiv.textContent = item.date.substring(0, 2) + ":" + item.date.substring(2, 4);
            timelineItem.appendChild(timeDiv);
        } else {
            // 占位
            const spacerDiv = document.createElement("div");
            spacerDiv.style.width = "70px";
            spacerDiv.style.order = isLeft ? 0 : 2;
            timelineItem.appendChild(spacerDiv);
        }

        // 内容区域
        const contentDiv = document.createElement("div");
        contentDiv.style.display = "flex";
        contentDiv.style.flexDirection = "column";
        contentDiv.style.gap = "2px";
        contentDiv.style.flex = "1";
        contentDiv.style.paddingLeft = isLeft ? "15px" : "0";
        contentDiv.style.paddingRight = isLeft ? "0" : "15px";
        contentDiv.style.order = 1;

        // 任务状态图标和描述
        const taskRow = document.createElement("div");
        taskRow.style.display = "flex";
        taskRow.style.alignItems = "center";
        taskRow.style.gap = "8px";

        // 任务状态图标
        const taskIcon = document.createElement("span");
        taskIcon.setAttribute("data-icon", item.taskType);
        taskIcon.textContent = item.taskType;
        taskRow.appendChild(taskIcon);

        // 任务描述
        const taskDesc = document.createElement("div");
        taskDesc.textContent = item.text;
        taskDesc.style.flex = "1";
        taskDesc.style.fontSize = "0.9em";
        taskDesc.style.color = "var(--text-normal)";
        taskRow.appendChild(taskDesc);

        contentDiv.appendChild(taskRow);

        // 印记文件链接
        if (item.stepName) {
            const stepLink = document.createElement("a");
            stepLink.href = `Nexus/印记/${CURRENT_JOURNEY.file.name}/${item.stepName}`;
            stepLink.className = "internal-link";
            stepLink.dataset['href'] = `Nexus/印记/${CURRENT_JOURNEY.file.name}/${item.stepName}`;
            stepLink.textContent = item.stepName;
            stepLink.style.fontSize = "0.8em";
            stepLink.style.marginLeft = "26px";
            
            // 根据流转类型设置颜色
            const flowType = item.stepType;
            const taskType = FLOW_TO_TASK_TYPE[flowType] || 'o';
            const color = TASK_TYPE_COLORS[taskType] || '#607D8B';
            stepLink.style.color = color;
            
            contentDiv.appendChild(stepLink);
        }

        timelineItem.appendChild(contentDiv);
        timelineList.appendChild(timelineItem);
    });

    return timelineSection;
}

// 创建左侧时间线（开始）
const startTimeline = createTimelineList("Start", start_normal, [], [], true, true);
timelinesContainer.appendChild(startTimeline);

// 创建右侧时间线（期望）
const expectTimeline = createTimelineList("Expect", expect_normal, expect_important, expect_urgent, false, true);
timelinesContainer.appendChild(expectTimeline);

// 响应式调整
const adjustStyles = () => {
    const containerWidth = container.offsetWidth;
    
    // 调整字体大小
    const baseFontSize = Math.max(10, Math.min(16, containerWidth / 50));
    journeyPanel.style.fontSize = `${baseFontSize}px`;
    
    // 调整时间线宽度
    if (containerWidth < 600) {
        timelinesContainer.style.flexDirection = "column";
    } else {
        timelinesContainer.style.flexDirection = "row";
    }
};

// 初始调整
adjustStyles();

// 监听窗口大小变化
window.addEventListener('resize', adjustStyles);
