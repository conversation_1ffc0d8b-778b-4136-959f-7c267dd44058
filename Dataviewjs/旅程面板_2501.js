/*
 * @描述: 旅程面板
 * @作者: roam1n
 * @版本: 0.0.1
 * @最后更新: 2025-01-22
 */

/**
 * 处理Markdown内容并计算灵石进度
 * @param {string} markdownContent - Markdown格式的文本内容
 * @param {number} maxReviewInterval - 最大复习间隔时间(单位:天)
 * @param {number} penaltyPerFailure - 单次失败的惩罚(减少的灵石)
 */

class JourneyPanel {
    constructor(dv, app) {
        this.dv = dv;
        this.app = app;
        this.totalProgressSegments = 7;
    }

    async render(markdownContent, options) {
        this.validateInput(markdownContent, options);

        const structuredContent = this.parseMarkdownStructure(markdownContent);
        const spiritStoneData = await this.generateSpiritStoneData(structuredContent);

        this.renderProgressBar(spiritStoneData, options);
        this.renderTables(spiritStoneData);
        await this.updateFrontmatter(spiritStoneData.totalSpiritStones);
    }

    validateInput(markdownContent, options) {
        if (typeof markdownContent !== 'string' || markdownContent.trim() === '') {
            throw new Error('markdownContent must be a non-empty string');
        }
        if (!Number.isInteger(options.maxReviewInterval) || options.maxReviewInterval <= 0) {
            throw new Error('maxReviewInterval must be a positive integer');
        }
        if (typeof options.penaltyPerFailure !== 'number' || options.penaltyPerFailure <= 0) {
            throw new Error('penaltyPerFailure must be a positive number');
        }
    }

    parseMarkdownStructure(content) {
        const lines = content.split('\n');
        const parsedStructure = [];
        let currentGroup = null;
        let currentItem = null;
        let currentSubItem = null;

        for (const line of lines) {
            if (line.startsWith('- ')) {
                currentGroup = this.createGroup(line);
                parsedStructure.push(currentGroup);
            } else if (line.startsWith('\t- ')) {
                currentItem = this.createItem(line);
                if (currentGroup) currentGroup.items.push(currentItem);
            } else if (line.startsWith('\t\t- ')) {
                currentSubItem = this.createSubItem(line);
                if (currentItem) currentItem.subItems.push(currentSubItem);
            } else if (line.startsWith('\t\t\t- ')) {
                this.updateSubItemScore(currentSubItem, line);
            }
        }

        return parsedStructure;
    }

    createGroup(line) {
        return { name: line.slice(2).trim(), items: [] };
    }

    createItem(line) {
        return { name: line.slice(3).trim(), subItems: [] };
    }

    createSubItem(line) {
        return { name: line.slice(4).trim(), score: 0 };
    }

    updateSubItemScore(subItem, line) {
        if (subItem) {
            subItem.score = parseFloat(line.slice(5).trim());
        }
    }

    async processGroup(group) {
        const rows = [];
        for (const item of group.items) {
            const row = this.processItem(item);
            rows.push(row);
        }
        return { name: group.name, rows };
    }

    processItem(item) {
        const row = {
            item: item.name,
            spiritStones: 0,
            progress: '0/0',
            subItems: item.subItems
        };

        const spiritStoneMatch = item.name.match(/\[灵石:(\d+)\]/);
        if (spiritStoneMatch) {
            row.spiritStones = parseInt(spiritStoneMatch[1]);
            row.item = row.item.replace(/\[灵石:\d+\]/, '').trim();
        }

        const fileMatch = row.item.match(/\[\[(.*?)\]\]/);
        if (fileMatch) {
            const file = this.dv.page(fileMatch[1]);
            row.progress = file?.灵石 ? `${file.灵石}/${row.spiritStones}` : `0/${row.spiritStones}`;
        }

        return row;
    }

    async generateSpiritStoneData(structuredContent) {
        const spiritStoneGroups = [];
        let totalSpiritStones = 0;
        let totalRelatedDiaries = [];

        for (const group of structuredContent) {
            const groupData = await this.processGroup(group);
            spiritStoneGroups.push(groupData);

            // 计算组内灵石总和
            groupData.rows.forEach(row => {
                const [current] = row.progress.split('/');
                totalSpiritStones += parseInt(current) || 0;

                // 收集相关日记
                const fileMatch = row.item.match(/\[\[(.*?)\]\]/);
                if (fileMatch) {
                    const file = this.dv.page(fileMatch[1]);
                    if (file?.file?.inlinks) {
                        const diaryDates = file.file.inlinks
                            .filter(link => link.path.startsWith("Nexus/日记"))
                            .map(link => link.path.replace("Nexus/日记/", "").slice(0, 10));
                        totalRelatedDiaries.push(...diaryDates);
                    }
                }
            });
        }

        return { groups: spiritStoneGroups, totalSpiritStones: parseFloat(totalSpiritStones.toFixed(1)), totalRelatedDiaries };
    }

    renderProgressBar(spiritStoneData, options) {
        const completedSegments = this.calculateProgressInfo(
            spiritStoneData.totalSpiritStones,
            spiritStoneData.totalRelatedDiaries,
            options.maxReviewInterval,
            options.penaltyPerFailure
        );

        const progressBarHtml = this.generateProgressBarHtml(completedSegments);
        this.dv.paragraph(progressBarHtml);
    }

    generateProgressBarHtml(completedSegments) {
        // 先用易读的方式生成HTML
        const html = `
            <div style="display: flex; justify-content: space-between; width: 100%; margin-top: 0px;">
                ${Array(this.totalProgressSegments).fill().map((_, i) => `
                    <div style="
                        width: 12%;
                        height: 8px;
                        background-color: ${i < completedSegments ? 'var(--interactive-accent)' : 'var(--text-muted)'};
                        border-radius: 4px;
                        opacity: 0.85;
                    "></div>
                `).join('')}
            </div>
        `;

        // 在返回前压缩HTML
        return html.replace(/\s+/g, ' ').trim();
    }

    renderTables(spiritStoneData) {
        for (const group of spiritStoneData.groups) {
            const tableRows = this.formatTableRows(group);
            this.renderGroupTable(group.name, tableRows);
        }
    }

    formatTableRows(group) {
        const tableRows = [];
        group.rows.forEach(row => {
            tableRows.push([
                row.item,
                row.progress,
                row.subItems[0]?.name || '',
                row.subItems[0]?.score || 0
            ]);

            row.subItems.slice(1).forEach(subItem => {
                tableRows.push(['', '', subItem.name, subItem.score]);
            });
        });
        return tableRows;
    }

    renderGroupTable(groupName, tableRows) {
        const paddedName = groupName + '.'.repeat(Math.max(0, (14 - groupName.length) * 1.9));
        this.dv.paragraph(this.dv.markdownTable(
            [paddedName, "..", "......", ".."],
            tableRows
        ));
    }

    async updateFrontmatter(totalSpiritStones) {
        const activeFile = this.app.workspace.getActiveFile();
        await this.app.fileManager.processFrontMatter(activeFile, fm => {
            fm.灵石 = totalSpiritStones;
        });
    }

    calculateProgressInfo(initValue, totalRelatedDiaries, maxReviewInterval, penaltyPerFailure) {
        const reviewDates = [...new Set(totalRelatedDiaries)].sort((a, b) => new Date(a) - new Date(b));
        let continuityScore = initValue - reviewDates.length;

        for (let i = 0; i < reviewDates.length - 1; i++) {
            const daysBetweenReviews = this.calculateDaysBetween(reviewDates[i], reviewDates[i + 1]);
            continuityScore = this.updateContinuityScore(continuityScore, daysBetweenReviews, maxReviewInterval, penaltyPerFailure);
        }

        return Math.min(this.totalProgressSegments, Math.floor(continuityScore));
    }

    calculateDaysBetween(date1, date2) {
        return (new Date(date2) - new Date(date1)) / (1000 * 60 * 60 * 24);
    }

    updateContinuityScore(score, daysBetween, maxInterval, penalty) {
        if (daysBetween > maxInterval) {
            score = Math.max(0, Number((score - (daysBetween - maxInterval) * penalty).toFixed(1)));
        }
        return Number((score + 1).toFixed(1));
    }
}

// 初始化并运行
const panel = new JourneyPanel(dv, app);
panel.render(input.markdownContent, {
    maxReviewInterval: input.maxReviewInterval,
    penaltyPerFailure: input.penaltyPerFailure
});
