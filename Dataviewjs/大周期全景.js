/*
 * @作者: roam1n
 * @版本: 0.1.1
 * @最后更新: 2025-05-23
 */

const TODAY = new Date();
TODAY.setHours(0, 0, 0, 0);

const TIME_BLOCK_RATE = {
    '练习': 2,
    '学习': 3,
    '实践': 5,
}

// 工具函数：获取当前大周期
function getCurrentMacroCycle(today) {
    return input?.marco ? dv.page(input?.marco) : dv.pages(`"Nexus/周期"`)
        .sort(p => p.file.name)
        .find(p => {
            const start = new Date(p.开始);
            const done = new Date(p.完成);
            return p.周期数量 > 0 &&
                   today.getTime() >= start.getTime() && 
                   today.getTime() <= done.getTime();
        });
}

// input {marco: 大周期文件名}
const MACRO = getCurrentMacroCycle(TODAY);

if (!MACRO) {
    dv.paragraph("未能获取大周期文件");
    return;
}

// 需要的文件数据：小周期数量及列表
const [MACRO_CODE, MACRO_NAME] = MACRO.file.name.split('=');
const MICRO_COUNT = parseInt(MACRO.周期数量);
const MICRO_LIST = dv.pages(`"Nexus/周期/${MACRO_CODE}"`).sort(p => p.file.name);
const SQUARE_EXP = 6;

// 创建容器
const container = dv.container;
container.id = "macro-cycle-panorama";
container.className = "container";

// 动态调整样式函数
function adjustSizesBasedOnContainer() {
    const containerWidth = container.offsetWidth;
    
    // 根据容器宽度计算合适的square大小
    let squareSize, squareGap, areaGap, areaPadding;
    
    squareSize = (containerWidth - 20) / Math.ceil(MICRO_COUNT * (10) / SQUARE_EXP * 1.074);
    squareGap = squareSize / 16;
    areaGap = squareSize / 4;
    areaPadding = squareSize / 8;

    // 更新CSS变量
    container.style.setProperty('--square-size', `${squareSize}px`);
    container.style.setProperty('--square-gap', `${squareGap}px`);
    container.style.setProperty('--area-gap', `${areaGap}px`);
    container.style.setProperty('--area-padding', `${areaPadding}px`);
}

// 初始调整
adjustSizesBasedOnContainer();

// 监听容器大小变化
const resizeObserver = new ResizeObserver(entries => {
    for (let entry of entries) {
        adjustSizesBasedOnContainer();
    }
});

resizeObserver.observe(container);

function getFileNameByPath(path) {
    if (!path) return null;
    if (!path?.split) return path;
    return path?.split('/').pop()?.replace('.md', '');
}

// 🗺️ 大周期全景图
const panorama = document.createElement("div");
panorama.className = "panorama";
container.appendChild(panorama);

// 标题区域
const titleSection = document.createElement("div");
titleSection.className = "title-section";
panorama.appendChild(titleSection);

// 添加年份标题
const macroTitle = document.createElement("a");
macroTitle.textContent = `${MACRO_NAME}-全景`;
macroTitle.href = `Nexus/周期/${MACRO.file.name}`;
macroTitle.className = "internal-link macro-title";
macroTitle.dataset['href'] = `Nexus/周期/${MACRO.file.name}`;
titleSection.appendChild(macroTitle);

// 添加统计信息
const statsInfo = document.createElement("div");
statsInfo.textContent = `共${MICRO_COUNT}个周期`;
statsInfo.className = "stats-info";
titleSection.appendChild(statsInfo);

const wishes = {}; // wishes[文件名] = [学习预期, 实践预期, 练习预期, 学习完成, 实践完成, 练习完成]
MACRO.file.tasks.filter(task => task.status === "J").forEach(task => {
    // 匹配模式: 数字-数字-数字 [[文件名]] 或 [[文件名]] 数字-数字-数字
    const pattern = /^\s*(?:(\d+)\s*-\s*(\d+)\s*-\s*(\d+)\s*\[\[(.*?)\]\]|\[\[(.*?)\]\]\s*(\d+)\s*-\s*(\d+)\s*-\s*(\d+))\s*$/;
    const match = task.text.match(pattern);

    if (match) {
        let filename, studyValue, practiceValue, exerciseValue;

        // 根据匹配位置确定数字和文件名
        if (match[1] !== undefined) {
            // 数字在前模式
            filename = match[4];
            studyValue = parseInt(match[1]);
            practiceValue = parseInt(match[2]);
            exerciseValue = parseInt(match[3]);
        } else {
            // 数字在后模式
            filename = match[5];
            studyValue = parseInt(match[6]);
            practiceValue = parseInt(match[7]);
            exerciseValue = parseInt(match[8]);
        }

        // 如果文件名已存在，累加数值；否则初始化
        if (wishes[filename]) {
            wishes[filename][0] += studyValue;    // 累加学习预期
            wishes[filename][1] += practiceValue; // 累加实践预期
            wishes[filename][2] += exerciseValue; // 累加练习预期
        } else {
            wishes[filename] = [studyValue, practiceValue, exerciseValue, 0, 0, 0];
        }
    }
});

// 计算学习、实践、练习的完成数据
MICRO_LIST.forEach(cycle => {
    if (cycle.学习 && cycle.学习.path) {
        const journey = dv.page(cycle.学习.path);
        const wish = getFileNameByPath(journey?.愿望?.path);
        if (wish) {
            wishes[wish][3] += 1;
        }
    }
    if (cycle.实践 && cycle.实践.path) {
        const journey = dv.page(cycle.实践.path);
        const wish = getFileNameByPath(journey?.愿望?.path);
        if (wish) {
            wishes[wish][4] += 1;
        }
    }
    if (cycle.练习 && cycle.练习.path) {
        const journey = dv.page(cycle.练习.path);
        const wish = getFileNameByPath(journey?.愿望?.path);
        if (wish) {
            wishes[wish][5] += 1;
        }
    }
});

// 处理wishes数据，创建排序后的数组
const wishesArray = Object.keys(wishes).map(filename => {
    return {
        name: filename,
        学习:   [wishes[filename][0], wishes[filename][3]], // 学习总数 / 学习完成数量（深色）
        实践: [wishes[filename][1], wishes[filename][4]], // 实践总数 / 实践完成数量（深色）
        练习:  [wishes[filename][2], wishes[filename][5]], // 练习总数 / 练习完成数量（深色）
    };
}).sort((a, b) => { 
    // 先以学习总数排序，相同则以实践总数排序，类推练习总数
    if (a.学习[0] !== b.学习[0]) {
        return b.学习[0] - a.学习[0];
    } else if (a.实践[0] !== b.实践[0]) {
        return b.实践[0] - a.实践[0];
    } else {
        return b.练习[0] - a.练习[0];
    }
}); 

// 创建颜色组合
const colorCombinations = [
    { light: 'rgba(229, 115, 115, 0.6)', dark: 'rgba(198, 40, 40, 0.8)' }, // 红色
    { light: 'rgba(129, 199, 132, 0.6)', dark: 'rgba(46, 125, 50, 0.8)' }, // 绿色
    { light: 'rgba(121, 134, 203, 0.6)', dark: 'rgba(40, 53, 147, 0.8)' }, // 蓝色
    { light: 'rgba(255, 213, 79, 0.5)', dark: 'rgba(249, 168, 37, 0.7)' }, // 黄色
    { light: 'rgba(186, 104, 200, 0.6)', dark: 'rgba(123, 31, 162, 0.8)' }, // 紫色
    { light: 'rgba(77, 208, 225, 0.6)', dark: 'rgba(0, 131, 143, 0.8)' }, // 青色
    { light: 'rgba(255, 183, 77, 0.6)', dark: 'rgba(239, 108, 0, 0.8)' }, // 橙色
    { light: 'rgba(149, 117, 205, 0.6)', dark: 'rgba(81, 45, 168, 0.8)' }  // 淡紫色
];

// 创建全景规划的容器
const macroPlan = document.createElement("div");
macroPlan.className = "macro-plan";
panorama.appendChild(macroPlan);

// 创建三个区域：练习、学习、实践
const categories = [
    { name: '学习', key: '学习', rate: TIME_BLOCK_RATE['学习'] },
    { name: '实践', key: '实践', rate: TIME_BLOCK_RATE['实践'] },
    { name: '练习', key: '练习', rate: TIME_BLOCK_RATE['练习'] }
];

const areas = [];
const allSquares = [];

categories.forEach((category, categoryIndex) => {
    // 创建区域容器
    const area = document.createElement("div");
    area.className = "area";
    area.style.gridTemplateRows = `repeat(${SQUARE_EXP}, 1fr)`;
    // area.style.gridTemplateColumns = `repeat(${Math.ceil(category.rate * 3)}, 1fr)`;
    macroPlan.appendChild(area);

    // 生成方格
    const squares = createSquares(area, MICRO_COUNT, category.rate);
    allSquares.push({ squares, category: category.key, categoryIndex });
    areas.push(area);
});

// 生成方格
function createSquares(area, count, rate) {
    // 先创建所有方格
    const squares = [];
    // 计算总的方格数：每个周期 * 倍率
    const totalSquares = count * rate;
    
    for (let i = 0; i < totalSquares; i++) {
        const square = document.createElement('div');
        area.appendChild(square);
        square.className = "square";
        
        // 添加索引标签 (每隔count/SQUARE_EXP*rate个显示一次)
        if ((i + 1) % Math.ceil((SQUARE_EXP * rate)) === 0) {
            const indexLabel = document.createElement("div");
            indexLabel.textContent = Math.ceil((i + 1) / rate).toString();
            indexLabel.className = "index-label";
            square.appendChild(indexLabel);
        }
        
        squares.push(square);
    }
    return squares;
}

// 方块填色
function fillSquares(squares, wish, areaType, wishIndex, rate) {
    let squareIndex = 0;
    const totalBlocks = wish[areaType][0]; // 总数量
    const completedBlocks = wish[areaType][1]; // 完成数量
    
    for (let i = 0; i < totalBlocks; i++) {
        // 每个块根据倍率生成多个square
        for (let j = 0; j < rate; j++) {
            if (squareIndex < squares.length) {
                if (i < completedBlocks) {
                    squares[squareIndex].style.backgroundColor = colorCombinations[wishIndex % colorCombinations.length].dark;
                } else {
                    squares[squareIndex].style.backgroundColor = colorCombinations[wishIndex % colorCombinations.length].light;
                }
                squares[squareIndex].title = `${wish.name}: ${areaType} ${(i)* rate + j + 1}/${totalBlocks*rate}`;
                squareIndex++;
            }
        }
    }
    return squareIndex;
}

// 填色逻辑重新设计
allSquares.forEach(({ squares, category, categoryIndex }) => {
    let currentSquareIndex = 0;
    const rate = categories[categoryIndex].rate;
    
    wishesArray.forEach((wish, wishIndex) => {
        const usedSquares = fillSquares(
            squares.slice(currentSquareIndex), 
            wish, 
            category, 
            wishIndex, 
            rate
        );
        currentSquareIndex += usedSquares;
    });
});

// 创建图例
const legend = document.createElement('div');
legend.className = "legend";
panorama.appendChild(legend);

// 创建图例项
const legendItems = document.createElement('div');
legendItems.className = "legend-items";
legend.appendChild(legendItems);

wishesArray.forEach((wish, index) => {
    const colorIndex = index % colorCombinations.length;
    const legendItem = document.createElement('div');
    legendItem.className = "legend-item";
    
    const colorSquare = document.createElement('div');
    colorSquare.className = "color-square";
    colorSquare.style.backgroundColor = colorCombinations[colorIndex].dark;
    legendItem.appendChild(colorSquare);

    const linkText = document.createElement('a');
    linkText.className = 'internal-link link-text';
    linkText.innerText = wish.name;
    linkText.dataset['href'] = wish.name;
    linkText.href = wish.name;
    legendItem.appendChild(linkText);
    
    // 添加统计信息
    const statsText = document.createElement('span');
    statsText.className = "stats-text";
    statsText.textContent = `${wish.学习[0]}-${wish.实践[0]}-${wish.练习[0]}`;
    legendItem.appendChild(statsText);

    legendItems.appendChild(legendItem);
});
