# 运动数据统计模板

## 模板文件
- `Templater/update-更新运动数据-usportdata.md`

## 功能
自动识别运动记录格式 `组数x每组次数x难度系数`，计算总运动值并更新前置元数据。

## 支持格式
```markdown
- 2x20x0.5 俯卧撑        # 数值在前
- 深蹲练习 3x15x0.8      # 数值在后  
- 1x30x1.0              # 仅数值
```

## 计算公式
运动值 = 组数 × 每组次数 × 难度系数

## 使用方法
1. 在文件中添加运动记录（列表格式）
2. 执行模板 `update-更新运动数据-usportdata`
3. 自动更新前置元数据中的 `运动` 字段

## 示例
```markdown
---
运动: 0  # 将被自动更新
---

- 2x20x0.5 俯卧撑      # 20 点
- 深蹲 3x15x0.8        # 36 点
- 1x30x1.0 平板支撑    # 30 点
```

执行后 `运动: 86`
