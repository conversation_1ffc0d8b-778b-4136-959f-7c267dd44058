<%*
let folder_path = "Nexus/印记" + "/";
let folder_name = tp.config.active_file.basename;

// 获取当前文件内容
let fileContent = await app.vault.read(app.workspace.getActiveFile());

// 移动后的文件path
let up = tp.frontmatter.up[0].replace(/\[\[(.*?)\]\]/, '$1');
let alias = tp.frontmatter.aliases?.length > 0 ? tp.frontmatter.aliases[0].replace(/[\s\S]*-\s*/, '') : folder_name;
let newFilePath = folder_path + up + "/" + alias;
let focus = tp.frontmatter.专注;

// 创建新的frontmatter
let newFrontmatter = `---
流转: 探索 ${focus ? `\n专注: ${focus}` : ""}
旅程: "[[${up}]]"
---`;

// 替换原有的frontmatter
let newContent = fileContent.replace(/---\n[\s\S]*?\n---/, newFrontmatter);

// 写入文件
await app.vault.modify(app.workspace.getActiveFile(), newContent);

// 移动文件
await tp.file.move(newFilePath);
-%>
