---
<%*
var folder_name = tp.config.active_file.basename;

// 选择旅程类型
var journeyTypeOptions = ['里程碑', '日期'];
var journeyType = await tp.system.suggester(journeyTypeOptions, journeyTypeOptions);

let newFileName = await tp.system.prompt("请输入旅程名称");
if (!newFileName || newFileName == null) {
    new Notice('旅程名称不能为空');
    return;
}

let newFilePath = "Nexus/旅程/" + folder_name + "/" + newFileName;
await tp.file.move(newFilePath);
-%>
愿望: "[[<% folder_name %>]]"
旅程类型: <% journeyType %>
<%* if (journeyType === "日期") { -%>
日期数量: 0
<%* } -%>
cdate: <% tp.date.now() %>
---

<%* if (journeyType === '里程碑') { -%>
```dvjs
dv.view('旅程面板')
```
- [Q]
<%* } -%>