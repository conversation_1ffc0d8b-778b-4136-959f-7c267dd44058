<%*
if (tp.config.run_mode !== 1) {
    new Notice('仅支持插入模式');
    return;
}

var dv = tp.app.plugins.plugins["dataview"].api;
var current = dv.page(tp.config.active_file.path);

// 累加当前印记文件下所有task的专注时间
var recordFocusTime = current.file.tasks.values.reduce((sum, task) => sum + (task.专注 || 0), 0);

// 计算当前印记文件下所有日记链接中记录的专注时间段的分钟数
var totalFocusTime = 0;
var diaryLinks = current.file.inlinks.values.filter(link => link.path.startsWith("Nexus/日记/"));
for (var i = 0; i < diaryLinks.length; i++) {
    var diary = dv.page(diaryLinks[i].path);
    diary.file.tasks.values.forEach(task => {
        var index = task.outlinks.findIndex(link => link.display == current.file.name);
        if (index > -1) {
            var regex = /[0-9]{1,2}[:：][0-9]{1,2}-[0-9]{1,2}[:：][0-9]{1,2}/;
            var match = task.text.match(regex);
            if (match) {
                var minutes = tp.user.calculateTimeDifference(match[0]);
                totalFocusTime += minutes;
                console.log(`[${diary.file.name}] ${task.outlinks[index].display} 的专注时间: ${minutes} 分钟`);
            }
        }
    });
}

console.log(`当前印记文件下所有task的专注时间: ${recordFocusTime} 分钟`);
console.log(`当前印记文件下所有日记链接中记录的专注时间段的分钟数: ${totalFocusTime} 分钟`);

var insertFocusTime = totalFocusTime - recordFocusTime < 0 ? 0 : totalFocusTime - recordFocusTime;
var insertText = `[专注::${insertFocusTime}]`;
-%>
<%insertText%>