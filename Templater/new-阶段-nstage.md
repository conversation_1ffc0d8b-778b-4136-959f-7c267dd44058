---
<%*
if (tp.config.run_mode !== 0) {
    new Notice('该模板只能在创建时使用');
    return;
}

let newFileName = await tp.system.prompt("请输入阶段名称");
let newFilePath = "Nexus/阶段/" + newFileName;
await tp.file.cursor_append(tp.file.selection() + "[[" + newFileName + "]]");
await tp.file.move(newFilePath);
-%>
开始: <% tp.date.now("YYYY-MM-DD") %>
持续: 14
cdate: <% tp.date.now() %>
---

```js
dv.view('阶段进度表', {start: "<% tp.date.now("YYYY-MM-DD") %>",
duration: 14, template: "14d" targets: {
A: {focus: 120, sport: 0, free: 0},
B: {focus: 120, sport: 0, free: 0},
C: {focus: 120, sport: 0, free: 0},
}, journeys: [
{name: "摘下梦中星辰", expect: 12},
{name: "日程管理的工作流", expect: 12},
]})
```
