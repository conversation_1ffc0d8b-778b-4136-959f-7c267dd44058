---
<%*
if (tp.config.run_mode !== 0) {
    new Notice('该模板只能在创建主题时使用');
    return;
}

let content_filename = await tp.system.prompt("请输入主题名称");
if (!content_filename || content_filename == null) {
    new Notice('主题名称不能为空');
    return;
}

var from_filename = "人生蓝图"
const parentPath = tp.config.active_file.path;
if (parentPath.startsWith('Nexus/主题/')) {
    from_filename = tp.config.active_file.basename;
}

let newFileName = content_filename;
let newFilePath = "Nexus/主题/" + newFileName + ".md";
await tp.file.move(newFilePath);
await tp.file.cursor_append(tp.file.selection() + "[[" + newFileName + "]]");
-%>
from:
  - "<% from_filename %>"
---

