---
<%*
if (tp.config.run_mode !== 0) {
    new Notice('该模板只能在创建印记时使用');
    return;
}

// 如果是Create模板，则检查母文件路径是否在愿望目录下
const parentPath = tp.config.active_file.path;
if (!parentPath.startsWith('Nexus/旅程/') && !parentPath.startsWith('Nexus/日记/')) {
    new Notice('该模板的母文件只能是 旅程/ 或 日记/ 目录下');
    return;
}

let folder_name = tp.config.active_file.basename;
if (!parentPath.startsWith('Nexus/旅程/')) {
    // 如果不是旅程路径，使用固定的子文件夹名
    folder_name = "地图漫步";  // 你可以根据需要修改这个固定名称
}

let prev_filename = tp.date.now("YYMM");
//如果旅程类型是日期类型，则使用年月日作为印记文件前缀
var frontmatter = app.metadataCache.getFileCache(tp.config.active_file).frontmatter;
if (frontmatter?.旅程类型 === '日期') {
    prev_filename = tp.date.now("YYMMDD");
}

let content_filename = await tp.system.prompt("请输入印记名称");
if (!content_filename || content_filename == null) {
    new Notice('印记名称不能为空');
    return;
}

let newFileName = prev_filename + "-" + content_filename;
let newFilePath = "Nexus/印记/" + folder_name + "/" + newFileName;
await tp.file.cursor_append(tp.file.selection() + "[[" + newFileName + "]]");
await tp.file.move(newFilePath);
-%>
旅程: "[[<% folder_name %>]]"
流转: 探索
cdate: <% tp.date.now() %>
---
