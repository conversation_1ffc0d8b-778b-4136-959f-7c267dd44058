<%*
// 获取当前文件名(不包含扩展名)
const currentFileName = tp.file.title;

// 获取当前文件的所有入链
const currentFile = tp.file.find_tfile(tp.file.path(true));
const inlinks = app.metadataCache.getBacklinksForFile(currentFile).data;

// 格式化数字为00格式
const formatNumber = (num) => {
    return Math.floor(num).toString().padStart(2, '0');
};

// 更新并计算所有印记的灵石值
let totalSoulstones = 0;
let content = await app.vault.read(currentFile);

if (inlinks instanceof Map) {
    for (let [linkFile, linkData] of inlinks) {
        const childFile = tp.file.find_tfile(linkFile);
        if (linkFile.includes(currentFileName)) {
            if (childFile.path.includes(`Nexus/印记/${currentFileName}`)) {
                console.log(`更新文件 ${childFile.path}`);
                const soulstones = await tp.user.更新印记灵石(tp, childFile);
                totalSoulstones += soulstones;
            }
        } else if (childFile.path.startsWith(`Nexus/日记/`)) {
            const dailyPath = childFile.path;
            const dailyFileContent = await tp.app.vault.read(childFile);
            const targetFileName = currentFile.basename;
            const targetFlowType = tp.frontmatter['流转'];
            const soulstones = await tp.user.计算当日灵石(tp, dailyPath, dailyFileContent, targetFileName, targetFlowType);
            totalSoulstones += soulstones;
        }
    }

    // 统一进行一次更新
    await app.vault.modify(currentFile, content);
}

// 更新当前文件的灵石值
if (totalSoulstones > 0) {
    tp.app.fileManager.processFrontMatter(
        currentFile,
        fm => { fm.灵石 = parseFloat(totalSoulstones.toFixed(1)); }
    );
}
-%>
