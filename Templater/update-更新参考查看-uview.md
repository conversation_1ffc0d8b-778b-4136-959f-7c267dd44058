<%*
const target_dir = "References" + "/";

if (tp.config.run_mode !== 1) {
    new Notice('仅支持插入模式');
    return;
}

const file = tp.config.active_file;

if (!file.path.startsWith(target_dir)) {
    new Notice('当前文件不在参考目录下');
    return;
}

const now = tp.date.now("YYYY-MM-DD HH:mm");

if (tp.frontmatter?.查看) {
    tp.app.fileManager.processFrontMatter(
        file,
        fm => { fm.查看 = now; }
    );
} else {
    // 获取当前文件内容
    let fileContent = await app.vault.read(file);

    // 将区域尾部的 --- 替换为  查看: YYYY-MM-DD HH:mm \n ---
    fileContent = fileContent.replace(/---\n[\s\S]*?\n---/, (match) => {
        return match.replace(/---$/, `查看: ${now}\n---`);
    });
    // 写入文件
    await app.vault.modify(file, fileContent);
}
-%>