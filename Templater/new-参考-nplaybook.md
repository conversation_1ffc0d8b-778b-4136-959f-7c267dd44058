---
<%*
if (tp.config.run_mode !== 0) {
    new Notice('该模板只能在创建时使用');
    return;
}

let newFileName = await tp.system.prompt("请输入参考名称");
var folder_name = tp.file.name;
var folder_path = tp.config.active_file.parent.path + "/" + folder_name + "/";
let newFilePath = folder_path + newFileName;
await tp.file.move(newFilePath);
await tp.file.cursor_append(tp.file.selection() + "[[" + newFileName + "]]");
-%>
关联: "[[<% folder_name %>]]"
参考类型: 笔记
cdate: <% tp.date.now() %>
---
