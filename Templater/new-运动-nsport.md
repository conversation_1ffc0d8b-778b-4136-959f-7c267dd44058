---
<%*
/*
 * @作者: roam1n
 * @版本: 1.0.0
 * @最后更新: 2025-06-20
 * @功能: 运动记录模板 - 检查今日运动记录是否存在，存在则打开，不存在则创建
 */

var curr_jounery_name = "运动锻炼-2506";

// 检查是否为新建文件模式
if (tp.config.run_mode !== 0) {
    new Notice('该模板只能在创建时使用');
    return;
}

// 生成今日日期格式 YYMMDD
const TODAY_DATE = tp.date.now("YYMMDD");
const SPORT_FILENAME = `${TODAY_DATE}-运动记录`;
const SPORT_FOLDERPATH = `Nexus/印记/${curr_jounery_name}`;

// 检查今日运动记录文件是否已存在
const existingFile = tp.app.metadataCache.getFirstLinkpathDest(SPORT_FILENAME, SPORT_FOLDERPATH);

if (existingFile) {
    // 文件已存在，直接打开
    new Notice(`今日运动记录已存在，正在打开: ${SPORT_FILENAME}`);
    await app.workspace.openLinkText(SPORT_FILENAME, SPORT_FOLDERPATH);
    return;
}

// 文件不存在，继续创建新文件
new Notice(`创建今日运动记录: ${SPORT_FILENAME}`);

// 移动文件到正确位置并重命名
await tp.file.cursor_append(tp.file.selection() + "- [N] [[" + SPORT_FILENAME + "|运动记录]]");
await tp.file.move(SPORT_FOLDERPATH + "/" + SPORT_FILENAME);
-%>
旅程: "[[<% curr_jounery_name %>]]"
流转: 运动
运动: 0
cdate: <% tp.date.now() %>
---
