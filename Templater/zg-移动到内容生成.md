<%*
let folder_path = "Attach/内容生成" + "/";
let folder_name = tp.config.active_file.basename;
let newFilePath = folder_path + (/^\d{12,}$/.test(folder_name) ? 
    (folder_name.length >= 12 ? 
        folder_name.slice(0,4) + '-' + folder_name.slice(4,8) + '-' + folder_name.slice(8)
        : folder_name) 
    : folder_name);

// 获取当前文件内容
let fileContent = await app.vault.read(app.workspace.getActiveFile());

// 提取需要的frontmatter
let upMatch = fileContent.match(/up:\n([\s\S]*?)(?=\n\w|---)/);
let upContent = upMatch ? upMatch[1] : '';
let aliasesMatch = fileContent.match(/aliases:\n([\s\S]*?)(?=\n\w|---)/);
let aliasesContent = aliasesMatch ? aliasesMatch[1] : '';

// 创建新的frontmatter
let newFrontmatter = `---
aliases:
${aliasesContent}
材料类型: 内容生成
up:
${upContent}
---`;

// 替换原有的frontmatter
let newContent = fileContent.replace(/---\n[\s\S]*?\n---/, newFrontmatter);

// 写入文件
await app.vault.modify(app.workspace.getActiveFile(), newContent);

// 移动文件
await tp.file.move(newFilePath);
-%>
