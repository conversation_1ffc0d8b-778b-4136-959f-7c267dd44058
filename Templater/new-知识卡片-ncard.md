---
<%*
if (tp.config.run_mode !== 0) {
    new Notice('该模板只能在创建知识卡片时使用');
    return;
}

var active_file = tp.config.active_file;
var folder_name = active_file.basename;
var active_file_cache = app.metadataCache.getFileCache(active_file);
var tags = active_file_cache.frontmatter?.tags || [];
-%>
tags:<%* if (tags.length > 0) { -%>
<%* tags.forEach(tag => { %>
  - <% tag %><%* }); %>
<%* } else { %>
  - none
<%* } -%>
卡片类型: 概念
up:
  - "[[<% folder_name %>]]"
cdate: <% tp.date.now() %>
---

<%* tp.file.move("Nexus/知识/" + tp.file.title) -%>