<%*
// 获取当前文件内容
let fileContent = await app.vault.read(app.workspace.getActiveFile());

// 解析现有的frontmatter和内容
let frontmatterMatch = fileContent.match(/^---\n([\s\S]*?)\n---/);
let existingFrontmatter = {};
let bodyContent = '';

if (frontmatterMatch) {
    // 解析现有的frontmatter
    let frontmatterText = frontmatterMatch[1];
    let lines = frontmatterText.split('\n');
    let currentKey = '';
    let currentValue = '';
    let inArray = false;
    
    for (let line of lines) {
        if (line.match(/^[^:\s]+:/)) {
            // 保存上一个键值对
            if (currentKey) {
                if (inArray) {
                    existingFrontmatter[currentKey] = currentValue.trim().split('\n').map(v => v.replace(/^\s*-\s*/, '').trim()).filter(v => v);
                } else {
                    existingFrontmatter[currentKey] = currentValue.trim();
                }
            }
            
            // 开始新的键值对
            let match = line.match(/^([^:]+):\s*(.*)$/);
            if (match) {
                currentKey = match[1].trim();
                currentValue = match[2];
                inArray = false;
                
                // 检查是否是数组开始
                if (currentValue === '' || currentValue === '[]') {
                    inArray = true;
                    currentValue = '';
                }
            }
        } else if (line.trim().startsWith('-') && inArray) {
            // 数组项
            currentValue += '\n' + line;
        } else if (currentKey && !inArray) {
            // 多行值
            currentValue += '\n' + line;
        }
    }
    
    // 保存最后一个键值对
    if (currentKey) {
        if (inArray) {
            existingFrontmatter[currentKey] = currentValue.trim().split('\n').map(v => v.replace(/^\s*-\s*/, '').trim()).filter(v => v);
        } else {
            existingFrontmatter[currentKey] = currentValue.trim();
        }
    }
    
    // 获取body内容
    bodyContent = fileContent.substring(frontmatterMatch[0].length).trim();
} else {
    // 没有frontmatter，整个文件都是内容
    bodyContent = fileContent.trim();
}

// 生成domain（如果URL存在且domain不存在）
let domain = existingFrontmatter.domain || '';
if (!domain && existingFrontmatter.URL) {
    try {
        let url = new URL(existingFrontmatter.URL);
        let hostname = url.hostname;
        // 移除www子域名，获取主域名
        domain = hostname.replace(/^www\./, '');
        // 如果有多个点，只保留最后两部分（处理子域名）
        let parts = domain.split('.');
        if (parts.length > 2) {
            domain = parts.slice(-2).join('.');
        }
    } catch (e) {
        // URL解析失败，保持空值
        domain = '';
    }
}

// 生成当前时间（ISO格式，带时区）
let currentTime = new Date().toISOString().replace('Z', '+08:00');

// 创建新的frontmatter，保留现有值，只为空值设置默认值
let newFrontmatter = {
    '材料类型': existingFrontmatter['材料类型'] || '书签',
    '主题': existingFrontmatter['主题'] || '',
    'URL': existingFrontmatter['URL'] || '',
    '查看': existingFrontmatter['查看'] || currentTime,
    '书签主题': existingFrontmatter['书签主题'] || '',
    '书签类型': existingFrontmatter['书签类型'] || ['工具'],
    '追踪': existingFrontmatter['追踪'] || '不追踪',
    'favicon': existingFrontmatter['favicon'] || '',
    'domain': domain || existingFrontmatter.domain || '',
    '游览次数': existingFrontmatter['游览次数'] || 1,
    '自评分': existingFrontmatter['自评分'] || 5
};

// 构建新的frontmatter文本
let frontmatterText = '---\n';
for (let [key, value] of Object.entries(newFrontmatter)) {
    if (Array.isArray(value)) {
        frontmatterText += `${key}:\n`;
        for (let item of value) {
            frontmatterText += `  - ${item}\n`;
        }
    } else {
        frontmatterText += `${key}: ${value}\n`;
    }
}
frontmatterText += '---';

// 构建完整的新内容
let newContent = frontmatterText + '\n' + bodyContent;

// 写入文件
await app.vault.modify(app.workspace.getActiveFile(), newContent);
-%>